#nano_configuration



from PySide6.QtWidgets import (
    QVBoxLayout, QGridLayout, QW<PERSON>t, QLabel, QCheckBox, QComboBox, QLineEdit, QPushButton,
    QFrame, QScrollArea, QHBoxLayout, QScrollBar, QMessageBox, QStatusBar, QSizePolicy, QSpacerItem, QFileDialog, QApplication, QDialog, QRadioButton,
    QTextEdit, QDialogButtonBox, QToolButton, QTabWidget, QFormLayout, QSpinBox, QDoubleSpinBox
)

from PySide6.QtGui import QIcon
from PySide6 import QtCore

from PySide6.QtCore import QObject, QThread, Signal, Slot, QCoreApplication, Qt, QMetaObject, QTimer
import ccxt
import os
import json
import re
import sqlite3
import threading
import pandas as pd
import ta
from datetime import datetime
from dotenv import load_dotenv
import sys
from styles import get_global_widget_style, get_text_area_style, get_frame_style, get_combobox_style, recolor_icon, get_standard_button_style, get_separator_style, color_for_message, get_dialog_style
from indicator_settings import get_schema, merge_with_defaults, validate, get_defaults
from nano_database import NanoTradingBotDatabase
from nano_dashboard import TerminalWidget, NanoDashboard
from nano_processor import PriceWatcher, send_shutdown_notifications
from reports import Reports
from indicator import indicators
from nano_notification import alert_telegram, alert_email


class TradingWorker(QObject):
    update_status = Signal(str, str)
    update_terminal = Signal(str, str, str)
    trading_finished = Signal()
    update_exchanges_balances_signal = Signal()
    add_trade_signal = Signal(str, tuple)
    update_statistics_signal = Signal(dict)  # Défini en haut de la classe
    # Nouveaux signaux pour la barre de statut améliorée
    update_enhanced_status = Signal(str, str, dict, int, int, float)  # main_status, function, symbols_info, exchanges_count, trades_count, progress



    def __init__(self, config, price_watcher, dashboard, nano_database):
        super().__init__()
        self.config = config
        self.nano_processor = price_watcher  # Assurez-vous de passer et stocker l'objet nano_processor
        self.dashboard = dashboard  # Stocker une référence au dashboard
        self.nano_database = nano_database

        self.trading_active = False
        self.trade_statistics = {'buy': 0, 'sell': 0, 'no_action': 0}

        self.traded_symbols = {}
        #self.total_trade_count = 0
        #self.load_total_trade_count()

        # Thread-local SQLite connection to avoid cross-thread QObject access (Linux segfault fix)
        self.db_path = getattr(self.nano_database, "db_path", "nano_trading_bot.db")
        self._db_lock = threading.Lock()
        self._conn = sqlite3.connect(self.db_path, check_same_thread=False)
        self._conn.row_factory = sqlite3.Row

    # -------------------- Thread-safe DB helpers --------------------
    def get_all_trades_threadsafe(self):
        try:
            return self.nano_database.get_all_trades_threadsafe()
        except Exception as e:
            print(f"TradingWorker DB error (get_all_trades): {e}")
            return []

    def get_trade_by_symbol_and_exchange_threadsafe(self, symbol, exchange):
        try:
            return self.nano_database.get_trade_by_symbol_and_exchange_threadsafe(symbol, exchange)
        except Exception as e:
            print(f"TradingWorker DB error (get_trade_by_symbol_and_exchange): {e}")
            return None

    def insert_trade_threadsafe(self, trade_data):
        try:
            return self.nano_database.insert_trade_threadsafe(trade_data)
        except Exception as e:
            print(f"TradingWorker DB error (insert_trade): {e}")
            return None

    def update_market_statistics_threadsafe(self, market_stats):
        try:
            return self.nano_database.update_market_statistics_threadsafe(market_stats)
        except Exception as e:
            print(f"TradingWorker DB error (update_market_statistics): {e}")

    def update_trade_statistics_threadsafe(self, trade_stats):
        try:
            return self.nano_database.update_trade_statistics_threadsafe(trade_stats)
        except Exception as e:
            print(f"TradingWorker DB error (update_trade_statistics): {e}")

    @Slot()
    def start_trading(self):
        self.trading_active = True
        self.run_trading_logic()

    @Slot()
    def stop_trading(self):
        self.trading_active = False




    def run_trading_logic(self):

        #self.load_previous_trades()  # Charger les trades précédents au démarrage

        while self.trading_active:
            # Obtenir les échanges sélectionnés depuis les checkboxes
            selected_exchanges = [exchange for exchange, checkbox in self.config.nano_trading_exchange_vars.items() if checkbox.isChecked()]

            # Mettre à jour la liste des échanges sélectionnés dans la configuration
            self.config.nano_trading_selected_exchanges = selected_exchanges

            selected_currency = self.config.get_selected_currency()
            num_trades = int(self.config.num_trades_entry.text())
            trade_amount = float(self.config.trade_amount_entry.text())

            if not selected_currency:
                self.update_status.emit("Error", "No currency selected for trading.")
                self.update_enhanced_status.emit("❌ Erreur", "Configuration", {}, 0, 0, 0.0)
                return

            if not selected_exchanges:
                self.update_status.emit("Error", "No exchanges selected for trading.")
                self.update_enhanced_status.emit("❌ Erreur", "No Exchanges", {}, 0, 0, 0.0)
                return

            # Émettre le statut initial avec le bon nombre d'échanges
            exchanges_count = len(selected_exchanges)
            self.update_enhanced_status.emit("🔄 Trading Active", "Initialization", {}, exchanges_count, 0, 0.0)




            # Vérifier le nombre de trades actifs
            active_trades_count = self.get_active_trades_count()  # Récupérer le nombre actuel de trades actifs

            # Mettre à jour le statut avec le nombre de trades actifs
            self.update_enhanced_status.emit("🔄 Trading Active", "Checking Trades", {}, exchanges_count, active_trades_count, 0.0)

            # Si on a déjà atteint le nombre maximum de trades actifs, attendre que des positions se libèrent
            if active_trades_count >= num_trades:
                self.update_terminal.emit("NANO|P", f"Maximum {num_trades} active trades reached.", "info")
                self.update_enhanced_status.emit("⏸️ Waiting", "Trade Limit Reached", {}, exchanges_count, active_trades_count, 100.0)
                # Attendre que des positions se libèrent
                while active_trades_count >= num_trades and self.trading_active :
                    QThread.sleep(5)
                    active_trades_count = self.get_active_trades_count()
                    self.update_enhanced_status.emit("⏸️ Waiting", "Awaiting Release", {}, exchanges_count, active_trades_count, 100.0)
                self.update_terminal.emit("NANO|P", "Active trade slots available, continuing trading.", "info")
                continue  # Recommencer la boucle après avoir attendu


            # Sinon, continuer la recherche de nouvelles opportunités pour combler les trades manquants
            self.update_terminal.emit("NANO|P", f"Only {active_trades_count} active trades. Searching for new trades.", "info")
            self.update_enhanced_status.emit("🔍 Searching", "New Opportunities", {}, exchanges_count, active_trades_count, 0.0)



            selected_timeframe_actions = self.config.get_selected_timeframe_actions()

            # Extraire uniquement les valeurs d'actions
            print("selected_timeframe_actions :",selected_timeframe_actions)
            """selected_timeframe_actions : [{'timeframe_depth_action': '15m', 'action': 'Buy'}, {'timeframe_depth_action': '1h', 'action': 'No Action'}]"""
            timeframe_depth_action = [item['timeframe_depth_action'] for item in selected_timeframe_actions]
            print("timeframe_depth_action :", timeframe_depth_action )
            """timeframe_depth_action : ['5m', '1h', '12h']"""
            selected_depth_action =  [item['action'] for item in selected_timeframe_actions]
            print("selected_depth_action: ", selected_depth_action)
            """selected_depth_action:  ['Buy', 'No Action', 'Buy']"""


            selected_timeframe = self.config.timeframe_combobox.currentText()

            selected_indicators = [indicator for indicator, var in self.config.indicator_vars.items() if var.isChecked()]

            # Log for user info
            self.update_terminal.emit("NANO|P", f"Selected Exchanges: {selected_exchanges}", "info")
            self.update_terminal.emit("NANO|P", f"Selected Currency: {selected_currency}", "info")
            self.update_terminal.emit("NANO|P", f"Selected Timeframe: {selected_timeframe}", "info")
            #self.update_terminal.emit("Info", f"Timeframe sélectionné: {selected_timeframe}\nTimeframes Depth sélectionnés: {timeframe_depth_action}", "info")
            self.update_terminal.emit("NANO|P", f"Selected Indicators: {selected_indicators}", "info")

            #excluded_coins = ['AEUR', 'DAI', 'EUR', 'EURPS', 'GBP', 'ACM', '1MBABYDOGE']
            #excluded_end_prefix = ['UP', 'DOWN']
            #excluded_start_prefix = '1000'

            # Récupérer les valeurs des tokens exclus et des préfixes
            excluded_coins = [token.strip().upper() for token in self.config.excluded_tokens_entry.text().split(',') if self.config.excluded_tokens_entry.text()]
            excluded_end_prefix = [prefix.strip().upper() for prefix in self.config.excluded_end_prefix_entry.text().split(',') if self.config.excluded_end_prefix_entry.text()]
            excluded_start_prefix = [prefix.strip().upper() for prefix in self.config.excluded_start_prefix_entry.text().split(',') if self.config.excluded_start_prefix_entry.text()]



            # Récupérer les indices de recherche
            start_index = int(self.config.search_index_start_entry.text()) if self.config.search_index_start_entry.text() else None
            end_index = int(self.config.search_index_end_entry.text()) if self.config.search_index_end_entry.text() else None



            total_exchanges = len(selected_exchanges)
            for exchange_idx, exchange_name in enumerate(selected_exchanges):
                print(f"Analyse de l'échange: {exchange_name}")  # Log pour chaque échange

                # Calculer la progression des exchanges
                exchange_progress = (exchange_idx / total_exchanges) * 100
                self.update_enhanced_status.emit("🔍 Analysis", f"Exchange: {exchange_name}", {}, exchanges_count, active_trades_count, exchange_progress)

                exchange = getattr(ccxt, exchange_name)({
                    'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                    'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                    'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
                })

                # Load markets ONLY (narrow try/except so later errors are not mislabeled)
                try:
                    # Measure duration for diagnostics
                    import time as _nt_time
                    _nt_t0 = _nt_time.time()
                    exchange.load_markets()
                    _nt_dt = _nt_time.time() - _nt_t0
                    self.update_terminal.emit("NANO|P", f"Markets loaded for {exchange_name} ({_nt_dt:.2f}s)", "info")
                    self.update_enhanced_status.emit("📊 Loading", f"Markets: {exchange_name} ({_nt_dt:.2f}s)", {}, exchanges_count, active_trades_count, exchange_progress)
                except Exception as e:
                    # Provide full traceback to help diagnose unclear status bar error
                    import traceback as _nt_tb, time as _nt_time
                    _nt_dt = (_nt_time.time() - _nt_t0) if '_nt_t0' in locals() else None
                    _nt_tb_str = _nt_tb.format_exc()
                    details = f"Error loading markets for {exchange_name}: {e}"
                    if _nt_dt is not None:
                        details += f" | duration={_nt_dt:.2f}s"
                    details += f"\n{_nt_tb_str}"
                    self.update_status.emit("Error", details)
                    continue

                # Build the pairs list for this exchange
                trade_scores = []
                try:
                    pairs = [
                        symbol for symbol in exchange.symbols
                        if symbol.endswith(f'/{selected_currency}')
                        and not any(symbol.endswith(f'{end}/{selected_currency}') for end in excluded_end_prefix)
                        and not any(symbol.startswith(f'{start}') for start in excluded_start_prefix)
                        and not any(stable in symbol for stable in excluded_coins)
                    ]

                    # Filtrage des paires en fonction des indices fournis
                    if start_index is not None and end_index is not None:
                        pairs = pairs[start_index:end_index]
                    elif start_index and end_index is None:
                        pairs = pairs[start_index:]  # Utiliser tous les éléments à partir de start_index
                    elif end_index and start_index is None:
                        pairs = pairs[:end_index]  # Utiliser tous les éléments jusqu'à end_index
                    elif start_index is None and end_index is None:
                        pairs = pairs[:]
                except Exception as e:
                    self.update_status.emit("Error", f"Error building pairs list for {exchange_name}: {e}")
                    continue

                # Récupérer les paramètres de filtrage de liquidité depuis l'interface
                min_volume = float(self.config.min_volume_entry.text()) if self.config.min_volume_entry.text() else self.config.min_volume_usdt
                max_spread = float(self.config.max_spread_entry.text()) if self.config.max_spread_entry.text() else self.config.max_spread_pct

                # Filtrage de liquidité - Étape préliminaire
                self.update_terminal.emit("NANO|P", f"Filtering {len(pairs)} symbols for liquidity...", "info")
                liquid_pairs = self.config.filter_liquid_symbols(exchange, pairs, min_volume, max_spread)
                self.update_terminal.emit("NANO|P", f"Found {len(liquid_pairs)} liquid symbols out of {len(pairs)}", "info")

                # Utiliser les paires filtrées pour l'analyse
                pairs = liquid_pairs

                # Étape 1: Évaluer chaque paire sur le timeframe principal
                total_pairs = len(pairs)
                for idx, pair in enumerate(pairs):

                        if not self.trading_active:
                            break
                        # Progression
                        progress = (idx + 1) / total_pairs * 100
                        symbols_info = {'current': idx + 1, 'total': total_pairs}

                        # Mettre à jour la barre de statut avec les informations détaillées
                        self.update_enhanced_status.emit(
                            "📈 Analysis",
                            f"Symbol: {pair}",
                            symbols_info,
                            exchanges_count,
                            active_trades_count,
                            progress
                        )

                        self.update_terminal.emit("NANO|P", f"{idx + 1}/{total_pairs} : {pair} ({progress:.2f}%)", "info")

                        # Récupérer les données sur le timeframe sélectionné
                        df = self.config.fetch_data(exchange, pair, selected_timeframe)
                        if df is None:
                            continue
                        self.config.calculate_indicators(df)


                        # Évaluer le marché et calculer le score
                        self.update_enhanced_status.emit(
                            "🧮 Evaluation",
                            f"Analysis: {pair}",
                            symbols_info,
                            exchanges_count,
                            active_trades_count,
                            progress
                        )

                        action, score = self.config.evaluate_market_and_trade(df, selected_indicators, self.config.dashboard, self.config.threshold, selected_timeframe, pair)

                        if action is not None:

                            # Calcul des statistiques pour tous les indicateurs
                            all_indicator_stats, all_type_stats = self.calculate_all_indicator_stats(indicators, action)

                            # Calcul des statistiques pour les indicateurs sélectionnés
                            selected_indicator_stats, selected_type_stats = self.calculate_selected_indicator_stats(selected_indicators, all_indicator_stats, action)

                            # Données supplémentaires pour la base de données
                            #current_price = df['close'].iloc[-1]  # Dernier prix de clôture comme prix actuel

                            if len(df) > 0:
                                current_price = df['close'].iloc[-1]
                            else:
                                self.update_terminal.emit("NANO|P", f"No data available for {pair} in timeframe {selected_timeframe}", "error")
                                continue

                            threshold = self.config.threshold

                            # Préparation des données à insérer dans la base de données
                            market_stats = {
                                'exchange': exchange_name,
                                'pair': pair,
                                'timeframe': selected_timeframe,
                                'action': action,
                                'score': score,

                                # Statistiques pour tous les indicateurs
                                'all_indicators': ', '.join([f"{ind}: {all_indicator_stats[ind]['category']}" for ind in all_indicator_stats]),
                                'all_indicators_stats': json.dumps(all_indicator_stats),
                                'all_indicators_type_stats': json.dumps(all_type_stats),

                                # Statistiques pour les indicateurs sélectionnés
                                'selected_indicators': ', '.join([f"{ind}: {selected_indicator_stats[ind]['category']}" for ind in selected_indicator_stats]),
                                'selected_indicators_stats': json.dumps(selected_indicator_stats),
                                'selected_indicators_type_stats': json.dumps(selected_type_stats),

                                # Valeurs de prix et seuil
                                'current_price': current_price,
                                'threshold': threshold,
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }

                            # Insertion dans la base de données (thread-safe, local connection)
                            self.update_market_statistics_threadsafe(market_stats)

                        # Enregistrement des statistiques de trading
                        trade_stats = {
                            'exchange': exchange_name,
                            'pair': pair,
                            'timeframe': selected_timeframe,
                            'current_buy': 1 if action == 'buy' else 0,
                            'current_sell': 1 if action == 'sell' else 0,
                            'current_no_action': 1 if action == 'no_action' else 0,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        self.update_trade_statistics_threadsafe(trade_stats)
                        self.update_statistics_signal.emit(trade_stats)  # Mettre à jour l'interface utilisateur avec les statistiques actuelles


                        selected_action = self.config.main_action_combobox.currentText()
                        print("selected_action: ", selected_action)
                        if selected_action == "No Action":
                                selected_action = "no_action"

                        if action == selected_action.lower(): #'sell','No Action':

                            trade_scores.append((pair, action, score))
                            self.update_terminal.emit("NANO|P", str(trade_scores), "info")

                # Étape 2: Trier les paires en fonction de leurs scores
                trade_scores.sort(key=lambda x: x[2])  # , reverse=True)

                # Étape 3: Pour chaque paire avec un bon score, évaluer les timeframes de profondeur
                for pair, action, score in trade_scores:
                        if ((action == 'buy' and score <= 10) or ( action == 'sell' and score >= -10 ) or ( action == 'no_action' and score > 0)):
                            combined_scores = []
                            # Boucle à travers chaque dictionnaire dans la liste
                            for item in selected_timeframe_actions:
                                depth_timeframe = item['timeframe_depth_action']  # Récupérer le timeframe
                                selected_depth_action = item['action']  # Récupérer l'action

                                df_depth = self.config.fetch_data(exchange, pair, depth_timeframe)
                                if df_depth is None:
                                    self.update_terminal.emit("NANO|P", f"No data for {pair} in the timeframe {selected_timeframe}")
                                    continue

                                self.config.calculate_indicators(df_depth)

                                # Évaluer sur le timeframe de profondeur
                                depth_action, depth_score = self.config.evaluate_market_and_trade(df_depth, selected_indicators, self.config.dashboard, self.config.threshold, depth_timeframe, pair)

                                # Récupérer l'action spécifique sélectionnée pour ce timeframe de profondeur
                                #print("depth_selected_action: ",selected_depth_action)

                                # Ajouter au score combiné uniquement si l'action est un 'buy'

                                if selected_depth_action == "No Action":
                                    selected_depth_action = "no_action"

                                if depth_action == selected_depth_action.lower():
                                    combined_scores.append((depth_timeframe, depth_score))
                                    self.update_terminal.emit("NANO|P", f"'{selected_depth_action}' found for {pair} in the timeframe {depth_timeframe}", "info")
                                #else:
                                    #self.update_terminal.emit("Info", f"Aucune action 'buy' trouvée pour {pair} dans le timeframe {depth_timeframe}", "info")

                            # Étape 4: Calculer le score pondéré avec les résultats des timeframes de profondeur
                            weighted_score = self.config.calculate_weighted_score(combined_scores)

                            # Étape 5: Exécuter un trade si le score pondéré est satisfaisant
                            if weighted_score is not None and pair not in self.config.unique_traded_pairs:
                                self.update_terminal.emit("NANO|P", f"Weighted Score for {pair} with Depth Action '{depth_action}' is: {weighted_score} ", "info")
                                self.execute_initial_trade(exchange, pair, 'buy', trade_amount, num_trades, weighted_score)
                                # Vérifier si on a atteint la limite de trades actifs

                                # Mettre à jour le nombre de trades actifs
                                active_trades_count = self.get_active_trades_count()
                                if active_trades_count >= num_trades:
                                    print(f"self.config.unique_traded_pairs: {self.config.unique_traded_pairs}")
                                    print(f"self.config.num_trades: {num_trades}")

                                    self.update_terminal.emit("NANO|P", "Trade limit reached, waiting for slots to free up.", "info")
                                    # Attendre que des positions se libèrent
                                    while (
                                        active_trades_count >= num_trades and self.trading_active and
                                        pair not in set(t['symbol'] for t in self.get_all_trades_threadsafe())
                                    ):
                                        QThread.sleep(5)
                                        active_trades_count = self.get_active_trades_count()
                                        print(f"Active Trades: {active_trades_count}/{num_trades}")
                                    self.update_terminal.emit("NANO|P ", "Active trade slots available, continuing trading.", "info")
                                    break  # Sortir de la boucle pour recommencer

            # Fin du cycle d'analyse, attente avant le prochain cycle
            self.update_enhanced_status.emit("⏳ Waiting", "Next Cycle", {}, exchanges_count, active_trades_count, 100.0)
            QThread.sleep(3)

        # Trading terminé
        self.update_enhanced_status.emit("⏹️ Stopped", "Trading Finished", {}, 0, 0, 0.0)
        self.trading_finished.emit()



    def get_active_trades_count(self):
        """Retourne le nombre de trades actifs."""
        active_trades = self.get_all_trades_threadsafe()  # Récupère les trades actifs depuis la base de données (thread-safe)
        return len(active_trades)




    def calculate_all_indicator_stats(self,indicators, action):
        """Calcule les statistiques pour tous les indicateurs."""
        all_indicator_stats = {}
        all_type_stats = {}

        # Initialisation des statistiques pour tous les indicateurs avec les noms de catégories
        for category, indicators_list in indicators.items():
            for ind in indicators_list:
                all_indicator_stats[ind] = {
                    'category': category,
                    'stats': {'buy': 0, 'sell': 0, 'no_action': 0}
                }

            # Initialisation des statistiques pour chaque catégorie
            all_type_stats[category] = {'buy': 0, 'sell': 0, 'no_action': 0}

        # Calcul des statistiques pour tous les indicateurs
        for indicator in all_indicator_stats.keys():
            if action == 'buy':
                all_indicator_stats[indicator]['stats']['buy'] += 1
            elif action == 'sell':
                all_indicator_stats[indicator]['stats']['sell'] += 1
            else:
                all_indicator_stats[indicator]['stats']['no_action'] += 1

            # Mise à jour des statistiques par catégorie
            category = all_indicator_stats[indicator]['category']
            if action == 'buy':
                all_type_stats[category]['buy'] += 1
            elif action == 'sell':
                all_type_stats[category]['sell'] += 1
            else:
                all_type_stats[category]['no_action'] += 1

        return all_indicator_stats, all_type_stats


    def calculate_selected_indicator_stats(self,selected_indicators, all_indicator_stats, action):
        """Calcule les statistiques pour les indicateurs sélectionnés."""
        selected_indicator_stats = {}
        selected_type_stats = {}

        for indicator in selected_indicators:
            selected_indicator_stats[indicator] = {
                'category': all_indicator_stats[indicator]['category'],
                'stats': {'buy': 0, 'sell': 0, 'no_action': 0}
            }

            if action == 'buy':
                selected_indicator_stats[indicator]['stats']['buy'] += 1
            elif action == 'sell':
                selected_indicator_stats[indicator]['stats']['sell'] += 1
            else:
                selected_indicator_stats[indicator]['stats']['no_action'] += 1

            # Mise à jour des statistiques par catégorie pour les indicateurs sélectionnés
            category = selected_indicator_stats[indicator]['category']
            if category not in selected_type_stats:
                selected_type_stats[category] = {'buy': 0, 'sell': 0, 'no_action': 0}

            if action == 'buy':
                selected_type_stats[category]['buy'] += 1
            elif action == 'sell':
                selected_type_stats[category]['sell'] += 1
            else:
                selected_type_stats[category]['no_action'] += 1

        return selected_indicator_stats, selected_type_stats



    def execute_initial_trade(self, exchange, symbol, action, trade_amount, num_of_trades, score):

        try:
            # Émettre le statut d'exécution du trade
            self.update_enhanced_status.emit(
                "💰 Execution",
                f"Trade {action.upper()}: {symbol}",
                {},
                0,
                0,
                0.0
            )

            # Récupérer les informations de ticker pour le symbole
            ticker = exchange.fetch_ticker(symbol)
            last_close = ticker['last']
            if exchange.name == 'mexc'.lower():
                exchange.options['createMarketBuyOrderRequiresPrice'] = False
            if action == 'buy':
                buy_price = last_close


            # Calculer le montant à trader basé sur le prix actuel
            amount_to_trade = trade_amount / last_close

            # Récupérer l'ID du trade existant, si disponible
            existing_trade_id = None  # Variable pour l'ID existant si trouvé
            existing_trade = self.get_trade_by_symbol_and_exchange_threadsafe(symbol, exchange.name.lower())
            if existing_trade:
                existing_trade_id = existing_trade['id']

            # Calculer le prix moyen d'achat
            average_buy_price = self.config.calculate_average_buy_price(symbol, existing_trade_id, last_close, amount_to_trade)

            self.update_terminal.emit("NANO|A-1", f"Placing {action} order for {amount_to_trade} {symbol} at price {last_close}", "info")

            # Passer l'ordre

            order = exchange.createOrder(symbol, 'market', action, amount_to_trade)


            self.update_terminal.emit("NANO|A-1", f"{action.capitalize()} order executed for {symbol}: {order}", "info")
            try:
                alert_telegram(f"{datetime.now().strftime('%Hh:%Mmin | %d-%m-%Y')}: {action.capitalize()} order executed for {symbol}\n{os.getenv('SERVER_NAME')}")
                alert_email(f"{datetime.now().strftime('%Hh:%Mmin | %d-%m-%Y')}: {action.capitalize()} order executed for {symbol}\n{os.getenv('SERVER_NAME')}")
            except Exception as e:
                print(f" {e}")

            # Calculer le pourcentage du trade
            trade_percent = ((last_close - average_buy_price) / average_buy_price) * 100



            # Calculer les prix d'achat et de vente pour chaque ligne avant de stocker les données
            for row in self.config.buy_rows:
                row['symbol'] = symbol
                row['exchange'] = exchange.name
                row['price_to_buy'] = self.config.calculate_price_to_buy(row, average_buy_price)

            for row in self.config.sell_rows:
                row['symbol'] = symbol
                row['exchange'] = exchange.name
                row['price_to_sell'] = self.config.calculate_price_to_sell(row, average_buy_price)

            # Sérialiser les configurations des buy_rows et sell_rows
            serialized_buy_rows = [
                {
                    "percentage_combobox": row["percentage_combobox"].currentText(),  # Extraire le texte sélectionné
                    "amount_entry": row["amount_entry"].text(),  # Extraire le texte de l'entrée
                    "price_to_buy": row["price_to_buy"],
                    "symbol": symbol,
                    "exchange": exchange.name
                }
                for row in self.config.buy_rows
            ]

            serialized_sell_rows = [
                {
                    "percentage_combobox": row["percentage_combobox"].currentText(),  # Extraire le texte sélectionné
                    "sell_percentage_entry": row["sell_percentage_entry"].text(),  # Extraire le texte de l'entrée
                    "price_to_sell": row["price_to_sell"],
                    "symbol": symbol,
                    "exchange": exchange.name
                }
                for row in self.config.sell_rows
            ]

            # L'ID sera généré automatiquement par la base de données (AUTOINCREMENT)




            # Préparer les données du trade (sans ID car la DB l'auto-génère)
            trade_data = {
                'exchange': exchange.name.lower(),
                'symbol': symbol,
                'timeframe': self.config.timeframe_combobox.currentText(),
                'base_token': symbol.split('/')[0],  # Exemple : ALCX dans ALCX/USDT
                'quote_token': symbol.split('/')[1],  # Exemple : USDT dans ALCX/USDT
                'amount_to_trade': amount_to_trade,
                'trade_amount': trade_amount,
                'buy_price': buy_price,
                'current_price': last_close,
                'trade_percent': trade_percent,
                'buy_rows': json.dumps(serialized_buy_rows),  # Sérialiser buy_rows en JSON
                'sell_rows': json.dumps(serialized_sell_rows),  # Sérialiser sell_rows en JSON
                'last_action': action,
                'trade_situation': 'in process',
                'buy_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'last_action_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),



            }

            # Insérer les données dans la base de données et récupérer l'ID réel généré
            trade_id = self.insert_trade_threadsafe(trade_data)

            # Émettre le signal pour mettre à jour l'interface utilisateur avec le nouveau trade
            trade_details = (
                trade_id,                  # ID du trade
                exchange.name.lower(),
                symbol,
                self.config.timeframe_combobox.currentText(),
                symbol.split('/')[0],
                amount_to_trade,
                symbol.split('/')[1],
                trade_amount,
                buy_price,
                last_close,
                trade_percent,
                action,
                'in process',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),

            )

            self.add_trade_signal.emit(exchange.name, trade_details)

            # Mise à jour des soldes après la transaction
            self.update_exchanges_balances_signal.emit()

            # Ajouter le trade au set unique_traded_pairs pour suivi
            self.config.add_unique_trade_pair(symbol)

        except Exception as e:
            #self.update_status.emit("Error", f"Error executing trade for {symbol}: {e}")
            #alert_telegram(f"Error executing trade for {symbol}: {e}")
            print(f"Error executing trade for {symbol}: {e}")
            self.update_terminal.emit("NANO|A-1", f"Error executing trade for {symbol}: {e}", "error")




class NanoConfiguration(QObject):  # Héritez de QObject pour pouvoir utiliser les signaux
    currency_selected_signal = Signal()


    def __init__(self, parent=None):
        super().__init__(parent)  # Appel correct à super()
        self.dark_mode = True
        # Initialiser la base de données
        self.db = NanoTradingBotDatabase()
        
        # setupUi n'est pas nécessaire car NanoConfiguration hérite de QObject, pas d'une classe UI
        self.setup_configuration()
        self.nano_trading_active = False
        self.unique_traded_pairs = set()  # Pour suivre les paires déjà tradées
        self.is_recovery_mode = False  # Indicateur de mode récupération

        self.excluded_tokens_list = ['AEUR', 'DAI', 'EUR', 'BTC', 'HBAR', 'EURPS', 'GBP', 'ACM', 'BTC', 'HBAR', '1MBABYDOGE', 'ASTR', 'ARDR', 'ASR', 'ACA', 'FDUSD', 'DOT3S']
        self.excluded_end_prefix_list = ['UP', 'DOWN']
        self.excluded_start_prefix_list = ['1000']
        self.search_index_start = 0
        self.search_index_end = 250

        self.threshold = 0.369
        
        # Valeurs par défaut pour les champs de configuration
        self.trade_amount = 10.0  # Montant par défaut pour le trading
        self.num_trades = 5  # Nombre de trades par défaut
        
        # Initialiser la structure des indicateurs depuis le module indicator
        self.indicators = indicators

        # Initialisation des widgets de mode de profit (pour les tests et l'utilisation sans interface)
        from PySide6.QtWidgets import QRadioButton, QLineEdit, QButtonGroup
        self.profit_mode_average = QRadioButton("Average Buy Price")
        self.profit_mode_average.setChecked(True)  # Mode par défaut
        self.profit_mode_last = QRadioButton("Last Buy Price")
        self.profit_mode_fixed = QRadioButton("Fixed Profit")
        self.fixed_profit_entry = QLineEdit()
        self.fixed_profit_entry.setText("0.15")

        # Créer un groupe pour les RadioButtons (comportement exclusif)
        self.profit_mode_group = QButtonGroup()
        self.profit_mode_group.addButton(self.profit_mode_average)
        self.profit_mode_group.addButton(self.profit_mode_last)
        self.profit_mode_group.addButton(self.profit_mode_fixed)

        # Paramètres de filtrage de liquidité
        self.min_volume_usdt = 1000000  # Volume minimum en USDT
        self.max_spread_pct = 0.1       # Spread maximum en pourcentage

        self.balance_initial_by_currency = {
            'USDT': 0,
            'USDC': 0,
            'BTC': 0,
        }
        self.nano_trading_exchange_vars = {}
        self.nano_trading_selected_exchanges = []
        self.timeframe_checkboxes = {}
        self.usdt_checkbox = None
        self.usdc_checkbox = None
        self.btc_checkbox = None
        self.custom_symbol_entry = None
        self.timeframe_combobox = None
        self.trade_amount_entry = None
        self.num_trades_entry = None
        self.threshold_entry = None
        self.min_volume_entry = None
        self.max_spread_entry = None
        self.buy_rows = []
        self.sell_rows = []
        self.start_button = None
        self.stop_button = None

        self.previous_buy_price = None
        self.previous_sell_price = None
        self.loaded_depth_timeframes = []
        # Approche défensive : utilise db_path de l'instance DB si disponible, sinon fallback
        self.db_path = getattr(self.db, "db_path", "nano_trading_bot.db")
        self.reports = Reports()
        self.status_bar = None
        self.nano_processor = PriceWatcher(self, self.db, None)  # Passer None pour le dashboard pour l'instant
        self.dashboard = NanoDashboard(self.status_bar, self)

        #self.dashboard = None
        self.nano_processor.dashboard = self.dashboard

        self.setup_configuration()
        self.connected_exchanges_widget = None  # Ajouter une référence pour ConnectedExchangesBalancesWidget

        self.unique_traded_pairs = set()


        # Connecter le signal à la méthode qui vérifie et redémarre le bot
        self.nano_processor.worker.restart_trading_signal.connect(self.restart_trading)
        # Connect application quit signal to cleanup method
        QCoreApplication.instance().aboutToQuit.connect(self.on_application_exit)

        self.trading_worker = TradingWorker(self, self.nano_processor, self.dashboard, self.db)
        self.trading_thread = None  # Initialiser l'attribut trading_thread

        # Connexion du signal update_statistics_signal au widget
        self.trading_worker.update_statistics_signal.connect(
            self.dashboard.exchange_symbol_trade_data_widget.update_widget_content
        )



    def check_and_ask_about_existing_database(self):
        """
        Vérifier s'il y a une base de données existante avec des trades
        et demander à l'utilisateur s'il veut la garder ou la supprimer.
        """
        print("🔍 DEBUG: check_and_ask_about_existing_database() called")
        print(f"CONFIG: Checking for existing database at: {self.db_path}")

        if not os.path.exists(self.db_path):
            print(f"CONFIG: No existing database found at: {self.db_path}")
            return

        print(f"CONFIG: Database file exists at: {self.db_path}")

        try:
            # UTILISER LA MÉTHODE THREAD-SAFE AU LIEU D'UNE NOUVELLE CONNEXION
            # Évite les conflits avec PriceWatcher qui utilise aussi la DB
            existing_trades = self.db.get_all_trades_threadsafe()
            
            if not existing_trades:
                print("Database exists but no active trades found - keeping empty database")
                return

            trade_count = len(existing_trades)

            # Obtenir des informations détaillées depuis les trades déjà récupérés (thread-safe)
            trades_info = []
            for trade in existing_trades[:5]:  # Limiter à 5 trades
                trades_info.append((
                    trade.get('exchange', ''),
                    trade.get('symbol', ''),
                    trade.get('buy_price', 0),
                    trade.get('current_price', 0),
                    trade.get('trade_percent', 0),
                    trade.get('last_action', '')
                ))

            # Construire le message informatif
            message_parts = [
                f"🔍 <b>Existing Database Found</b><br><br>",
                f"📊 <b>{trade_count} active trade{'s' if trade_count > 1 else ''} detected:</b><br><br>"
            ]

            # Déterminer le mode de profit depuis le fichier de recovery
            recovery_profit_mode = self._get_recovery_profit_mode()

            for i, trade in enumerate(trades_info[:3]):  # Afficher max 3 trades
                exchange, symbol, buy_price, current_price, trade_percent, last_action = trade
                status_icon = "📈" if trade_percent and trade_percent > 0 else "📉" if trade_percent and trade_percent < 0 else "⚖️"

                # Formater selon le mode de profit détecté
                if recovery_profit_mode == "fixed_profit":
                    # Mode Fixed Profit : calculer et afficher en USDT
                    try:
                        # Récupérer amount_to_trade depuis les trades déjà récupérés (thread-safe)
                        amount_to_trade = 0.001
                        for existing_trade in existing_trades:
                            if (existing_trade.get('symbol') == symbol and 
                                existing_trade.get('exchange') == exchange):
                                amount_to_trade = float(existing_trade.get('amount_to_trade', 0.001))
                                break

                        # Calculer le profit en USDT
                        profit_usdt = (float(current_price) - float(buy_price)) * amount_to_trade
                        fixed_profit_target = self._get_recovery_fixed_profit_amount()

                        profit_display = f"{profit_usdt:+.4f}/{fixed_profit_target:.3f} USDT"
                    except Exception as e:
                        print(f"Error calculating USDT display: {e}")
                        # En mode Fixed Profit, ne pas afficher de pourcentage en fallback
                        fixed_profit_target = self._get_recovery_fixed_profit_amount()
                        profit_display = f"0.000/{fixed_profit_target:.3f} USDT"
                else:
                    # Mode pourcentage
                    profit_display = f"{trade_percent:+.2f}%"

                message_parts.append(
                    f"   {status_icon} <b>{symbol}</b> on {exchange}: "
                    f"{profit_display} ({last_action})<br>"
                )

            if trade_count > 3:
                message_parts.append(f"   ... and {trade_count - 3} more trade{'s' if trade_count - 3 > 1 else ''}<br>")

            message_parts.extend([
                f"<br>🔄 <b>Recovery Options:</b><br>",
                f"• <b>Keep Database</b>: Enable recovery system for interrupted trades<br>",
                f"• <b>Delete Database</b>: Start fresh (all trade data will be lost)<br><br>",
                f"💡 <i>If you experienced a power outage or crash, choose 'YES' to recover your trades.</i><br><br>",
                f"<b>What would you like to do?</b>"
            ])

            full_message = "".join(message_parts)

            # Demander à l'utilisateur
            from PySide6.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                None,
                'Existing Database Found',
                full_message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes  # Par défaut, garder la DB
            )

            if reply == QMessageBox.StandardButton.Yes:
                print(f"✅ User chose to keep database with {trade_count} trades")
                print("🔄 Recovery system will be available")
            else:
                print(f"🗑️ User chose to delete database with {trade_count} trades")
                try:
                    # Étape 1: Fermer toutes les connexions à la base de données
                    print("🔒 Closing all database connections...")

                    # Fermer la connexion de l'objet database actuel
                    if hasattr(self, 'db') and self.db:
                        try:
                            # Appeler la méthode de fermeture des connexions de la classe Database
                            if hasattr(self.db, 'close_all_connections'):
                                self.db.close_all_connections()
                            print("✅ Database connections closed")
                        except Exception as close_error:
                            print(f"Warning: Error closing database connections: {close_error}")

                    # Étape 2: Arrêter le processor s'il utilise la DB
                    if hasattr(self, 'nano_processor') and self.nano_processor:
                        try:
                            print("⏹️ Stopping processor to release database...")
                            self.nano_processor.stop_watching()
                        except Exception as processor_error:
                            print(f"Warning: Error stopping processor: {processor_error}")

                    # Étape 3: Forcer la fermeture de TOUTES les connexions SQLite
                    try:
                        import gc
                        # Forcer le garbage collection pour libérer toutes les références
                        gc.collect()

                        # Essayer de forcer la fermeture avec une connexion exclusive
                        temp_conn = sqlite3.connect(self.db_path, timeout=0.1)
                        temp_conn.execute("PRAGMA journal_mode=DELETE;")  # Forcer la fermeture du WAL
                        temp_conn.close()

                        # Nettoyer les fichiers WAL et SHM s'ils existent
                        wal_file = self.db_path + "-wal"
                        shm_file = self.db_path + "-shm"

                        for aux_file in [wal_file, shm_file]:
                            if os.path.exists(aux_file):
                                try:
                                    os.remove(aux_file)
                                    print(f"🗑️ Removed {aux_file}")
                                except Exception as aux_error:
                                    print(f"Warning: Could not remove {aux_file}: {aux_error}")

                        print("🔒 Forced SQLite connections cleanup")
                    except Exception as cleanup_error:
                        print(f"Warning: SQLite cleanup failed: {cleanup_error}")

                    # Étape 4: Attendre un peu pour que les processus se terminent
                    import time
                    time.sleep(1.5)  # Augmenter le délai

                    # Étape 5: Tenter la suppression avec retry
                    max_retries = 5  # Augmenter le nombre de tentatives
                    for attempt in range(max_retries):
                        try:
                            os.remove(self.db_path)
                            print(f"✅ Database deleted: {self.db_path}")
                            break
                        except PermissionError as perm_error:
                            if attempt < max_retries - 1:
                                print(f"⚠️ Attempt {attempt + 1} failed, retrying in 2 seconds...")
                                time.sleep(2)  # Augmenter le délai entre tentatives
                            else:
                                print(f"❌ SUPPRESSION ÉCHOUÉE après {max_retries} tentatives")
                                print("🔒 Le fichier est toujours verrouillé par un autre processus")
                                raise perm_error

                    # Étape 5: Réinitialiser complètement l'objet database après suppression
                    print("🔄 Reinitializing database...")
                    from nano_database import NanoTradingBotDatabase
                    self.db = NanoTradingBotDatabase()

                    # Reconnecter les signaux si le dashboard existe
                    if hasattr(self, 'dashboard') and self.dashboard:
                        try:
                            self.db.trade_removed_from_db_signal.connect(
                                self.dashboard.exchanges_trades_tables.remove_trade_from_table
                            )
                            print("🔗 Database signals reconnected")
                        except Exception as signal_error:
                            print(f"Warning: Could not reconnect database signals: {signal_error}")

                    print("✅ Database reinitialized successfully")

                except Exception as e:
                    print(f"❌ ÉCHEC DE LA SUPPRESSION DE LA BASE DE DONNÉES: {e}")
                    print("\n🔧 ÉTAPES DE DÉPANNAGE:")
                    print("1. 🛑 Arrêter TOUTES les opérations de trading (cliquer 'Stop Trading')")
                    print("2. ⏳ Attendre 10 secondes que tous les processus s'arrêtent")
                    print("3. 🔄 Redémarrer l'application complètement")
                    print("4. 🔄 Essayer de supprimer la base de données à nouveau")
                    print("5. 📁 En dernier recours: fermer l'app et supprimer manuellement 'nano_trading_bot.db'")

                    # Afficher un message dans l'interface utilisateur
                    if hasattr(self, 'display_to_status_bar'):
                        self.display_to_status_bar("Error",
                            "SUPPRESSION ÉCHOUÉE - Redémarrer l'application et réessayer")

                    # La base de données sera gardée par défaut
                    print("\n💾 LA BASE DE DONNÉES EST CONSERVÉE - Vos trades sont préservés")
                    print("🔄 Le système de récupération reste disponible")

        except Exception as e:
            print(f"Error checking existing database: {e}")
            # En cas d'erreur, garder la base de données par sécurité
            print("Keeping database due to error during check")

    def _get_recovery_profit_mode(self):
        """Récupère le mode de profit depuis le fichier de recovery"""
        try:
            recovery_file = "config_recovery.nta"
            if os.path.exists(recovery_file):
                with open(recovery_file, 'r') as f:
                    config_data = json.load(f)
                    profit_modes = config_data.get('profit_modes', {})

                    if profit_modes.get('fixed_profit', False):
                        return "fixed_profit"
                    elif profit_modes.get('last_price', False):
                        return "last_price"
                    else:
                        return "average_price"
            else:
                # Si pas de fichier de recovery, utiliser le mode actuel
                return self.get_profit_mode()
        except Exception as e:
            print(f"Error reading recovery profit mode: {e}")
            return "average_price"  # Mode par défaut

    def _get_recovery_fixed_profit_amount(self):
        """Récupère le montant de profit fixe depuis le fichier de recovery"""
        try:
            recovery_file = "config_recovery.nta"
            if os.path.exists(recovery_file):
                with open(recovery_file, 'r') as f:
                    config_data = json.load(f)
                    profit_modes = config_data.get('profit_modes', {})
                    fixed_amount = profit_modes.get('fixed_profit_amount', '0.15')
                    return float(fixed_amount) if fixed_amount else 0.15
            else:
                # Si pas de fichier de recovery, utiliser la valeur actuelle
                return self.get_fixed_profit_amount()
        except Exception as e:
            print(f"Error reading recovery fixed profit amount: {e}")
            return 0.15  # Valeur par défaut


    def restart_trading(self):
        # Arrêter le trading actuel si nécessaire
        self.stop_nano_trading()
        # Redémarrer le processus de recherche de nouveaux symboles
        self.start_nano_trading()


    def setup_configuration(self):
        self.status_bar = self.create_status_bar()
        if not hasattr(self, 'dashboard') or self.dashboard is None:
            self.dashboard = NanoDashboard(self.status_bar, self)



    def __del__(self):
        """try:
            if hasattr(self, 'dashboard') and self.dashboard:
                self.stop_nano_trading()
        except AttributeError:
            print("Le tableau de bord n'est pas défini ou déjà supprimé.")"""
        pass

    def set_dashboard(self, dashboard):
        self.dashboard = dashboard
        self.connected_exchanges_widget = dashboard.connected_exchanges_widget  # Lier le widget ConnectedExchangesBalancesWidget

    def create_status_bar(self):
        """Crée et configure une barre d'état."""
        status_bar = QStatusBar()

        # Ajouter un label à la barre d'état pour afficher des messages
        self.status_message = QLabel("Ready")  # Message par défaut
        status_bar.addPermanentWidget(self.status_message)

        return status_bar

    def display_to_nano_terminal(self, title, message, level):
        if self.dashboard:
            self.dashboard.display_message_to_terminal(title, message, level)

    def display_to_status_bar(self, title, message):
        if hasattr(self, 'status_bar') and self.status_bar:
            self.status_bar.showMessage(f"{title}: {message}", 3000)

    def update_enhanced_status_bar(self, main_status, function, symbols_info, exchanges_count, trades_count, progress):
        """Met à jour la barre de statut améliorée avec les informations détaillées du trading"""
        # Vérifier que nous sommes dans le thread principal pour éviter les erreurs de timer
        from PySide6.QtCore import QThread, QCoreApplication
        if QThread.currentThread() != QCoreApplication.instance().thread():
            print("Warning: update_enhanced_status_bar called from non-GUI thread, ignoring")
            return

        if hasattr(self, 'main_window') and self.main_window:
            # Utiliser la référence directe à la fenêtre principale
            if hasattr(self.main_window, 'update_enhanced_status'):
                self.main_window.update_enhanced_status(
                    main_status=main_status,
                    function=function,
                    symbols_info=symbols_info,
                    exchanges_count=exchanges_count,
                    trades_count=trades_count,
                    progress=progress
                )
    
    def add_separator(self, scroll_layout, row):
        """
        Ajoute un séparateur avec espacement dans le layout donné.
        """
        # Ajouter un espace avant le séparateur
        scroll_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        # Créer le séparateur (ligne horizontale)
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName('separator')
        separator.setStyleSheet(get_separator_style(True))

        # Ajouter le séparateur au layout
        scroll_layout.addWidget(separator, row, 0, 1, 3)
        row += 1

        # Ajouter un espace après le séparateur
        scroll_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        return row




    def create_configuration_widget(self):
        from PySide6.QtWidgets import QButtonGroup
        load_dotenv()
        widget = QWidget()
        layout = QVBoxLayout(widget)
        widget.setStyleSheet("background-color: rgba(100, 100, 100, 0.05); border-radius: 10px;")

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setObjectName('scrollAreaStyle')
        scroll_bar = QScrollBar()
        scroll_bar.setObjectName('scrollBarStyle')

        scroll_content = QWidget()
        scroll_layout = QGridLayout(scroll_content)
        scroll_content.setStyleSheet(get_global_widget_style())

        exchanges_list = [exchange for exchange in ccxt.exchanges if os.getenv(f'{exchange.upper()}_API_KEY') and os.getenv(f'{exchange.upper()}_SECRET_KEY')]

        row = 0
        scroll_layout.addWidget(QLabel("Select Exchanges:"), row, 0, 1, 2)
        row += 1
        exchange_col = 0
        for exchange in exchanges_list:
            checkbox = QCheckBox(exchange)
            self.nano_trading_exchange_vars[exchange] = checkbox
            scroll_layout.addWidget(checkbox, row, exchange_col)

            # Connecter chaque checkbox d'exchange pour mettre à jour les balances disponibles et le texte des tokens
            checkbox.stateChanged.connect(lambda state, ex=exchange: self.check_exchange_balance(ex))
            exchange_col += 1
            if exchange_col >= 2:
                exchange_col = 0
                row += 1
        row += 1


        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        scroll_layout.addWidget(QLabel("Trade Amount ($):"), row, 0)
        scroll_layout.addWidget(QLabel("Number of Trades:"), row, 1)
        row += 1
        self.trade_amount_entry = QLineEdit()
        self.trade_amount_entry.setPlaceholderText("Enter trade amount")
        self.trade_amount_entry.setObjectName("customLineEdit")
        self.trade_amount_entry.setStyleSheet(get_text_area_style(True))
        self.trade_amount_entry.setText(str(self.trade_amount))
        scroll_layout.addWidget(self.trade_amount_entry, row, 0)

        self.num_trades_entry = QLineEdit()
        self.num_trades_entry.setPlaceholderText("Enter number of trades")
        self.num_trades_entry.setObjectName("customLineEdit")
        self.num_trades_entry.setStyleSheet(get_text_area_style(True))
        self.num_trades_entry.setText(str(self.num_trades))
        scroll_layout.addWidget(self.num_trades_entry, row, 1)
        row += 1

         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1


        scroll_layout.addWidget(QLabel("Select Symbols:"), row, 0)
        row += 1
        symbols_layout = QVBoxLayout()
        self.usdt_checkbox = QCheckBox("USDT")
        symbols_layout.addWidget(self.usdt_checkbox)
        self.usdc_checkbox = QCheckBox("USDC")
        symbols_layout.addWidget(self.usdc_checkbox)
        self.btc_checkbox = QCheckBox("BTC")
        symbols_layout.addWidget(self.btc_checkbox)
        symbols_widget = QWidget()
        symbols_widget.setLayout(symbols_layout)
        scroll_layout.addWidget(symbols_widget, row, 0)

        # Connecter les changements de checkboxes symboles pour mettre à jour le label des tokens
        self.usdt_checkbox.stateChanged.connect(self.update_tokens_info_label)
        self.usdt_checkbox.stateChanged.connect(self.on_currency_selected)
        self.usdt_checkbox.stateChanged.connect(self.update_currency_label)
        self.usdc_checkbox.stateChanged.connect(self.update_tokens_info_label)
        self.usdc_checkbox.stateChanged.connect(self.on_currency_selected)
        self.usdc_checkbox.stateChanged.connect(self.update_currency_label)
        self.btc_checkbox.stateChanged.connect(self.update_tokens_info_label)
        self.btc_checkbox.stateChanged.connect(self.on_currency_selected)
        self.btc_checkbox.stateChanged.connect(self.update_currency_label)

        row += 1

        scroll_layout.addWidget(QLabel("Custom Symbol:"), row, 0)
        row += 1
        self.custom_symbol_entry = QLineEdit()
        self.custom_symbol_entry.setPlaceholderText("Enter custom symbol")
        self.custom_symbol_entry.setObjectName("customLineEdit")
        self.custom_symbol_entry.setStyleSheet(get_text_area_style(True))
        scroll_layout.addWidget(self.custom_symbol_entry, row, 0)

        # Connecter le changement de texte de custom_symbol_entry à la fonction de mise à jour
        self.custom_symbol_entry.textChanged.connect(self.update_tokens_info_label)
        self.custom_symbol_entry.textChanged.connect(self.on_currency_selected)
        self.custom_symbol_entry.textChanged.connect(self.update_currency_label)


        row += 1

        # Créer un QVBoxLayout pour afficher les QLabel au-dessus de chaque QLineEdit
        excluded_layout = QHBoxLayout()

        # Layout pour chaque paire QLabel/QLineEdit
        excluded_tokens_layout = QVBoxLayout()
        excluded_end_prefix_layout = QVBoxLayout()
        excluded_start_prefix_layout = QVBoxLayout()

        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        # Label et QLineEdit pour 'Excluded Tokens'
        excluded_tokens_label = QLabel("Excluded Tokens:")
        self.excluded_tokens_entry = QLineEdit()
        self.excluded_tokens_entry.setPlaceholderText("Enter tokens to exclude")
        self.excluded_tokens_entry.setObjectName("customLineEdit")
        self.excluded_tokens_entry.setStyleSheet(get_text_area_style(True))
        self.excluded_tokens_entry.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.excluded_tokens_entry.setText(', '.join(self.excluded_tokens_list))

        excluded_tokens_layout.addWidget(excluded_tokens_label)
        excluded_tokens_layout.addWidget(self.excluded_tokens_entry)

        # Label et QLineEdit pour 'Excluded End Prefix'
        excluded_end_prefix_label = QLabel("Excluded End Prefix:")
        self.excluded_end_prefix_entry = QLineEdit()
        self.excluded_end_prefix_entry.setPlaceholderText("Enter end prefixes to exclude")
        self.excluded_end_prefix_entry.setObjectName("customLineEdit")
        self.excluded_end_prefix_entry.setStyleSheet(get_text_area_style(True))
        self.excluded_end_prefix_entry.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.excluded_end_prefix_entry.setText(', '.join(self.excluded_end_prefix_list))

        excluded_end_prefix_layout.addWidget(excluded_end_prefix_label)
        excluded_end_prefix_layout.addWidget(self.excluded_end_prefix_entry)

        # Label et QLineEdit pour 'Excluded Start Prefix'
        excluded_start_prefix_label = QLabel("Excluded Start Prefix:")
        self.excluded_start_prefix_entry = QLineEdit()
        self.excluded_start_prefix_entry.setPlaceholderText("Enter start prefixes to exclude")
        self.excluded_start_prefix_entry.setObjectName("customLineEdit")
        self.excluded_start_prefix_entry.setStyleSheet(get_text_area_style(True))
        self.excluded_start_prefix_entry.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.excluded_start_prefix_entry.setText(', '.join(self.excluded_start_prefix_list))

        excluded_start_prefix_layout.addWidget(excluded_start_prefix_label)
        excluded_start_prefix_layout.addWidget(self.excluded_start_prefix_entry)

        # Ajouter chaque layout de section à excluded_layout
        excluded_layout.addLayout(excluded_tokens_layout)
        excluded_layout.addLayout(excluded_end_prefix_layout)
        excluded_layout.addLayout(excluded_start_prefix_layout)

        # Ajouter le layout vertical au scroll_layout
        scroll_layout.addLayout(excluded_layout, row, 0, 1, 3)
        row += 1

        # Définir all_timeframes avant son utilisation
        all_timeframes = ["1s", "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M", "3M"]

        # Ajout de l'affichage avant le texte "Search Index Range:"
        selected_exchanges = self.nano_trading_selected_exchanges
        selected_currency = self.get_selected_currency()
        tokens_info_text = self.get_tokens_information(selected_exchanges, selected_currency)


        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        # Créer le QLabel pour afficher les informations des tokens avec retour à la ligne
        self.tokens_info_label = QLabel(tokens_info_text)  # Stocker le label dans self pour mise à jour
        self.tokens_info_label.setWordWrap(True)  # Activer le retour à la ligne automatique
        self.tokens_info_label.setStyleSheet("font-size: 12px; font-weight: bold; color: black;")  # Style du texte

        # Ajouter le QLabel au layout avant "Search Index Range:"
        scroll_layout.addWidget(self.tokens_info_label, row, 0, 1, 3)
        row += 1  # Incrémenter la ligne pour la prochaine section

        # Ajouter les QLineEdit pour la plage de recherche
        scroll_layout.addWidget(QLabel("Search Index Range:"), row, 0, 1, 3)
        row += 1

        index_range_layout = QHBoxLayout()

        self.search_index_start_entry = QLineEdit()
        self.search_index_start_entry.setPlaceholderText("Start index (e.g., 0)")
        self.search_index_start_entry.setObjectName("customLineEdit")
        self.search_index_start_entry.setStyleSheet(get_text_area_style(True))
        self.search_index_start_entry.setText(str(self.search_index_start))
        index_range_layout.addWidget(self.search_index_start_entry)

        self.search_index_end_entry = QLineEdit()
        self.search_index_end_entry.setPlaceholderText("End index (e.g., 50)")
        self.search_index_end_entry.setObjectName("customLineEdit")
        self.search_index_end_entry.setStyleSheet(get_text_area_style(True))
        self.search_index_end_entry.setText(str(self.search_index_end))
        index_range_layout.addWidget(self.search_index_end_entry)

        scroll_layout.addLayout(index_range_layout, row, 0, 1, 3)
        row += 1


         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        scroll_layout.addWidget(QLabel("Select Timeframe:"), row, 0)
        scroll_layout.addWidget(QLabel("Threshold:"), row, 1)
        row += 1
        self.timeframe_combobox = QComboBox()
        self.timeframe_combobox.setObjectName("customComboBox")
        self.timeframe_combobox.addItems(all_timeframes)
        self.timeframe_combobox.setStyleSheet(get_combobox_style(True))
        scroll_layout.addWidget(self.timeframe_combobox, row, 0, 1, 1)

        # Ajouter un combobox pour sélectionner l'action principale (Buy, Sell, No Action)

        self.threshold_entry = QLineEdit()
        self.threshold_entry.setPlaceholderText("Enter threshold")
        self.threshold_entry.setObjectName("customLineEdit")
        self.threshold_entry.setStyleSheet(get_text_area_style(True))
        self.threshold_entry.setText(str(self.threshold))
        scroll_layout.addWidget(self.threshold_entry, row, 1, 1, 1)
        row += 1

        # Ajouter les paramètres de filtrage de liquidité
        scroll_layout.addWidget(QLabel("Liquidity Filtering:"), row, 0, 1, 2)
        row += 1

        # Volume minimum
        scroll_layout.addWidget(QLabel("Min Volume (USDT):"), row, 0, 1, 1)
        self.min_volume_entry = QLineEdit()
        self.min_volume_entry.setPlaceholderText("Minimum volume in USDT")
        self.min_volume_entry.setObjectName("customLineEdit")
        self.min_volume_entry.setStyleSheet(get_text_area_style(True))
        self.min_volume_entry.setText(str(self.min_volume_usdt))
        scroll_layout.addWidget(self.min_volume_entry, row, 1, 1, 1)
        row += 1

        # Spread maximum
        scroll_layout.addWidget(QLabel("Max Spread (%):"), row, 0, 1, 1)
        self.max_spread_entry = QLineEdit()
        self.max_spread_entry.setPlaceholderText("Maximum spread percentage")
        self.max_spread_entry.setObjectName("customLineEdit")
        self.max_spread_entry.setStyleSheet(get_text_area_style(True))
        self.max_spread_entry.setText(str(self.max_spread_pct))
        scroll_layout.addWidget(self.max_spread_entry, row, 1, 1, 1)
        row += 1

         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        scroll_layout.addWidget(QLabel("Select Timeframes Depth:"), row, 0, 1, 2)
        row += 1
        self.timeframe_checkboxes_frame = QFrame()
        self.timeframe_checkboxes_frame.setStyleSheet(get_frame_style(True))
        self.timeframe_checkboxes_layout = QGridLayout(self.timeframe_checkboxes_frame)
        scroll_layout.addWidget(self.timeframe_checkboxes_frame, row, 0, 1, 2)
        row += 1

        self.timeframe_checkboxes = {}
        col = 0
        depth_row = 0
        for timeframe in all_timeframes:
            checkbox = QCheckBox(timeframe)
            self.timeframe_checkboxes[timeframe] = checkbox
            self.timeframe_checkboxes_layout.addWidget(checkbox, depth_row, col)
            depth_row += 1
            if depth_row >= 5:
                depth_row = 0
                col += 1
        self.initialize_timeframe_checkboxes()

         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1


        # Section "Select Main Action" ajustée pour placer le ComboBox en dessous du label
        row += 1

        # Ajouter le label dans le scroll_layout
        label_main_action = QLabel("Select Main Action:")
        scroll_layout.addWidget(label_main_action, row, 0, 1, 3)

        # Incrémenter la ligne
        row += 1

        # Ajouter le ComboBox juste en dessous du label
        self.main_action_combobox = QComboBox()
        self.main_action_combobox.setObjectName("customComboBox")
        self.main_action_combobox.addItems(["Buy", "Sell", "No Action"])
        self.main_action_combobox.setCurrentIndex(0)  # Par défaut 'Buy'

        self.main_action_combobox.setMinimumWidth(80)  # Largeur adaptative
        self.main_action_combobox.setStyleSheet(get_combobox_style(True))
        scroll_layout.addWidget(self.main_action_combobox, row, 0, 1, 3)

        row += 1  # Incrémenter pour l'élément suivant





        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

       # Ajouter un label pour la sélection des actions des timeframes de profondeur
        label_timeframe_actions = QLabel("Select Timeframes Depth Actions:")
        scroll_layout.addWidget(label_timeframe_actions, row, 0, 1, 1)  # Placer le label dans la première colonne
        row += 1  # Incrémenter la ligne pour le prochain ajout



        # Créer un QGridLayout pour organiser dynamiquement les labels et comboboxes
        self.timeframe_action_grid = QGridLayout()
        scroll_layout.addLayout(self.timeframe_action_grid, row, 0, 1, 2)  # Ajouter le QGridLayout au scroll_layout
        row += 1  # Incrémenter pour laisser de l'espace après



        # Appel initial pour générer les comboboxes en fonction des cases cochées au démarrage
        #update_timeframe_actions()
        for checkbox in self.timeframe_checkboxes.values():
            checkbox.stateChanged.connect(self.update_timeframe_actions)

        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1



        scroll_layout.addWidget(QLabel("Select Indicators:"), row, 0, 1, 3)
        row += 1

        # Ajouter les boutons Select All / Deselect All pour les indicateurs
        indicators_buttons_layout = QHBoxLayout()

        select_all_indicators_btn = QPushButton("Select All")
        select_all_indicators_btn.setStyleSheet(get_standard_button_style())
        select_all_indicators_btn.clicked.connect(self.select_all_indicators)
        indicators_buttons_layout.addWidget(select_all_indicators_btn)

        deselect_all_indicators_btn = QPushButton("Deselect All")
        deselect_all_indicators_btn.setStyleSheet(get_standard_button_style())
        deselect_all_indicators_btn.clicked.connect(self.deselect_all_indicators)
        indicators_buttons_layout.addWidget(deselect_all_indicators_btn)

        indicators_buttons_layout.addStretch()  # Pousser les boutons vers la gauche

        # Créer un widget pour contenir les boutons
        indicators_buttons_widget = QWidget()
        indicators_buttons_widget.setLayout(indicators_buttons_layout)
        scroll_layout.addWidget(indicators_buttons_widget, row, 0, 1, 3)
        row += 1

        indicators_frame = QFrame()
        indicators_frame.setStyleSheet(get_frame_style(True))
        indicators_layout = QGridLayout(indicators_frame)
        col = 0
        ind_row = 0
        self.indicator_vars = {}
        # Initialize indicator settings store if not present
        if not hasattr(self, 'indicator_settings') or not isinstance(getattr(self, 'indicator_settings'), dict):
            self.indicator_settings = {}
        for category, indics in self.indicators.items():


            category_label = QLabel(category)
            category_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
            indicators_layout.addWidget(category_label, ind_row, 0, 1, 3)
            ind_row += 1

            for indicator in indics:

                # Create a row widget containing the indicator checkbox and a gear button (after the name)
                row_widget = QWidget()
                row_layout = QHBoxLayout(row_widget)
                row_layout.setContentsMargins(0, 0, 0, 0)
                row_layout.setSpacing(6)

                checkbox = QCheckBox(indicator)
                checkbox.setChecked(True)
                # Prevent the checkbox from expanding so the gear stays right after the text
                try:
                    checkbox.setSizePolicy(QSizePolicy.Maximum, QSizePolicy.Preferred)
                except Exception:
                    pass
                self.indicator_vars[indicator] = checkbox
                row_layout.addWidget(checkbox, 0, Qt.AlignLeft)

                gear_btn = QToolButton()
                gear_btn.setAutoRaise(True)
                # Use recolored gear icon for dark/light consistency (pass True as nearby code does)
                try:
                    gear_icon_path = recolor_icon(os.path.join("icons", "gear.png")), (255, 255, 255), f"gear-icon-dark.png"
                    gear_btn.setIcon(QIcon(gear_icon_path))
                except Exception:
                    gear_btn.setText("⚙")
                gear_btn.setToolTip(f"Configure {indicator}")
                try:
                    # Keep the button compact and inline with text
                    gear_btn.setToolButtonStyle(Qt.ToolButtonIconOnly)
                except Exception:
                    pass
                # Style the icon button using existing helper
                try:
                    from styles import get_icon_button_style
                    gear_btn.setStyleSheet(get_icon_button_style(True))
                except Exception:
                    pass
                gear_btn.clicked.connect(lambda checked=False, name=indicator: self.open_indicator_settings_dialog(name))
                row_layout.addWidget(gear_btn, 0, Qt.AlignLeft)

                indicators_layout.addWidget(row_widget, ind_row, col)
                col += 1
                if col >= 3:
                    col = 0
                    ind_row += 1
            col = 0
            ind_row += 1

        scroll_layout.addWidget(indicators_frame, row, 0, 1, 3)
        row += 1


         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1


                # Section des modes de profit
        profit_mode_frame = QFrame()
        profit_mode_frame.setStyleSheet(get_frame_style(True))
        profit_mode_layout = QVBoxLayout(profit_mode_frame)

        # Titre
        profit_title = QLabel("Profit Mode Configuration")
        profit_title.setStyleSheet("font-weight: bold; color: #8A7A9A; font-size: 14px; margin-bottom: 10px;")
        profit_mode_layout.addWidget(profit_title)

        # RadioButtons pour les 3 modes
        modes_layout = QHBoxLayout()

        # Mode 1: Average Buy Price (par défaut)
        self.profit_mode_average = QRadioButton("Average Buy Price")
        self.profit_mode_average.setChecked(True)  # Mode par défaut
        self.profit_mode_average.setStyleSheet("color: #8A7A9A; font-size: 12px;")

        # Mode 2: Last Buy Price
        self.profit_mode_last = QRadioButton("Last Buy Price")
        self.profit_mode_last.setStyleSheet("color: #8A7A9A; font-size: 12px;")

        # Mode 3: Fixed Profit
        self.profit_mode_fixed = QRadioButton("Fixed Profit")
        self.profit_mode_fixed.setStyleSheet("color: #8A7A9A; font-size: 12px;")

        modes_layout.addWidget(self.profit_mode_average)
        modes_layout.addWidget(self.profit_mode_last)
        modes_layout.addWidget(self.profit_mode_fixed)
        modes_layout.addStretch()
        profit_mode_layout.addLayout(modes_layout)

        # Fixed Profit Amount Section
        fixed_profit_layout = QHBoxLayout()

        self.fixed_profit_label = QLabel("Fixed Profit Amount:")
        self.fixed_profit_label.setStyleSheet("color: white; font-weight: bold; font-size: 11px;")
        fixed_profit_layout.addWidget(self.fixed_profit_label)

        self.fixed_profit_entry = QLineEdit()
        self.fixed_profit_entry.setPlaceholderText("0.15")
        self.fixed_profit_entry.setText("0.15")
        self.fixed_profit_entry.setStyleSheet(get_text_area_style(True))
        self.fixed_profit_entry.setMaximumWidth(100)
        self.fixed_profit_entry.setVisible(False)  # Caché par défaut
        self.fixed_profit_entry.textChanged.connect(self.on_fixed_profit_changed)
        fixed_profit_layout.addWidget(self.fixed_profit_entry)

        # Label pour afficher la devise sélectionnée (dynamique)
        self.currency_label = QLabel("USDT")  # Par défaut à USDT
        self.currency_label.setStyleSheet("color: white; font-size: 11px;")
        self.currency_label.setVisible(False)  # Caché par défaut
        fixed_profit_layout.addWidget(self.currency_label)

        fixed_profit_layout.addStretch()
        profit_mode_layout.addLayout(fixed_profit_layout)

        # Stop Loss Section
        stop_loss_main_layout = QVBoxLayout()

        # Créer un groupe de boutons pour le stop loss
        self.stop_loss_group = QButtonGroup(self)
        
        # Stop Loss Enable (utiliser QCheckBox pour permettre toggle on/off)
        self.stop_loss_enabled_radio = QCheckBox("Enable Stop Loss")
        self.stop_loss_enabled_radio.setStyleSheet("color: white; font-size: 11px;")
        self.stop_loss_enabled_radio.setChecked(False)  # Désactivé par défaut
        stop_loss_main_layout.addWidget(self.stop_loss_enabled_radio)
        # Ne pas ajouter le checkbox au stop_loss_group car il doit pouvoir être toggle

        # Stop Loss Mode Selection (visible seulement si activé)
        self.stop_loss_mode_frame = QFrame()
        stop_loss_mode_layout = QVBoxLayout(self.stop_loss_mode_frame)

        stop_loss_mode_radio_layout = QHBoxLayout()

        # Créer un groupe pour les options de mode de stop loss
        self.stop_loss_mode_group = QButtonGroup(self)
        
        self.stop_loss_with_buy_rows_radio = QRadioButton("With Buy Rows")
        self.stop_loss_with_buy_rows_radio.setStyleSheet("color: white; font-size: 10px;")
        self.stop_loss_with_buy_rows_radio.setChecked(True)
        self.stop_loss_mode_group.addButton(self.stop_loss_with_buy_rows_radio)

        self.stop_loss_without_buy_rows_radio = QRadioButton("Without Buy Rows")
        self.stop_loss_without_buy_rows_radio.setStyleSheet("color: white; font-size: 10px;")
        self.stop_loss_mode_group.addButton(self.stop_loss_without_buy_rows_radio)

        stop_loss_mode_radio_layout.addWidget(self.stop_loss_with_buy_rows_radio)
        stop_loss_mode_radio_layout.addWidget(self.stop_loss_without_buy_rows_radio)
        stop_loss_mode_radio_layout.addStretch()

        stop_loss_mode_layout.addLayout(stop_loss_mode_radio_layout)

        # Stop Loss Percentage Input
        stop_loss_input_layout = QHBoxLayout()

        stop_loss_input_label = QLabel("Stop Loss %:")
        stop_loss_input_label.setStyleSheet("color: white; font-weight: bold; font-size: 10px;")
        stop_loss_input_layout.addWidget(stop_loss_input_label)

        self.stop_loss_input = QLineEdit()
        self.stop_loss_input.setPlaceholderText("Enter percentage (e.g., 5.0)")
        self.stop_loss_input.setStyleSheet(get_text_area_style(True))
        self.stop_loss_input.setMaximumWidth(100)
        self.stop_loss_input.setText("0.0")
        self.stop_loss_input.textChanged.connect(self.on_stop_loss_changed)
        stop_loss_input_layout.addWidget(self.stop_loss_input)

        percent_label = QLabel("%")
        percent_label.setStyleSheet("color: white; font-size: 10px;")
        stop_loss_input_layout.addWidget(percent_label)

        stop_loss_input_layout.addStretch()

        stop_loss_mode_layout.addLayout(stop_loss_input_layout)

        self.stop_loss_mode_frame.setVisible(False)  # Caché par défaut
        stop_loss_main_layout.addWidget(self.stop_loss_mode_frame)

        profit_mode_layout.addLayout(stop_loss_main_layout)

        # Connecter les signaux du stop loss (après création des widgets)
        self.stop_loss_enabled_radio.toggled.connect(self.on_stop_loss_enabled_changed)
        self.stop_loss_with_buy_rows_radio.toggled.connect(self.on_stop_loss_mode_changed)
        self.stop_loss_without_buy_rows_radio.toggled.connect(self.on_stop_loss_mode_changed)

        # Créer un groupe pour les RadioButtons (comportement exclusif)
        if not hasattr(self, 'profit_mode_group'):
            self.profit_mode_group = QButtonGroup()
        self.profit_mode_group.addButton(self.profit_mode_average)
        self.profit_mode_group.addButton(self.profit_mode_last)
        self.profit_mode_group.addButton(self.profit_mode_fixed)

        # Connecter les événements pour tous les modes
        self.profit_mode_average.toggled.connect(self.on_profit_mode_changed)
        self.profit_mode_last.toggled.connect(self.on_profit_mode_changed)
        self.profit_mode_fixed.toggled.connect(self.on_profit_mode_changed)

        scroll_layout.addWidget(profit_mode_frame, row, 0, 1, 2)
        row += 1

        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1




        scroll_layout.addWidget(QLabel("Buy / Sell Configuration:"), row, 0, 1, 2)
        row += 1




        # Pour la configuration d'achat
        self.buy_setup_layout = QVBoxLayout()
        self.buy_setup_frame = QFrame()  # Stocker comme attribut de classe
        self.buy_setup_frame.setStyleSheet(get_frame_style(True))
        self.buy_setup_frame.setLayout(self.buy_setup_layout)
        self.add_buy_row(self.buy_setup_layout)



        # Pour la configuration de vente
        self.sell_setup_layout = QVBoxLayout()
        self.sell_setup_frame = QFrame()  # Stocker comme attribut de classe
        self.sell_setup_frame.setStyleSheet(get_frame_style(True))
        self.sell_setup_frame.setLayout(self.sell_setup_layout)
        self.add_sell_row(self.sell_setup_layout)

        scroll_layout.addWidget(self.buy_setup_frame, row, 0)
        scroll_layout.addWidget(self.sell_setup_frame, row, 1)
        row += 1

        # Appliquer l'état initial des modes de profit
        self.on_profit_mode_changed()

        # Ajouter un layout pour les boutons d'enregistrement et d'ouverture
        file_button_frame = QFrame()
        file_button_frame.setStyleSheet(get_frame_style(True))
        file_button_layout = QHBoxLayout(file_button_frame)

        self.save_button = QPushButton()
        self.save_button.setIcon(QIcon(os.path.join("icons", "save-icon.png")))  # Assurez-vous que l'icône existe
        self.save_button.setStyleSheet("border: none;")  # Pas de bordure pour un look d'icône
        self.save_button.setMinimumSize(35, 35)
        self.save_button.setMaximumSize(45, 45)
        self.save_button.setToolTip("Save Configuration to File")
        self.save_button.clicked.connect(self.save_configuration_to_file)

        self.open_button = QPushButton()
        self.open_button.setIcon(QIcon(os.path.join("icons", "open-icon.png")))  # Assurez-vous que l'icône existe
        self.open_button.setStyleSheet("border: none;")
        self.open_button.setMinimumSize(35, 35)
        self.open_button.setMaximumSize(45, 45)
        self.open_button.setToolTip("Open Configuration File")
        self.open_button.clicked.connect(self.open_configuration_file)

        self.recovery_button = QPushButton()
        self.recovery_button.setIcon(QIcon(os.path.join("icons", "recovery-icon.png")))  # Icône de récupération
        self.recovery_button.setStyleSheet("border: none;")
        self.recovery_button.setMinimumSize(35, 35)
        self.recovery_button.setMaximumSize(45, 45)
        self.recovery_button.setToolTip("Load Recovery Configuration (config_recovery.nta)")
        self.recovery_button.clicked.connect(self.load_recovery_configuration)

        file_button_layout.addWidget(self.save_button)
        file_button_layout.addWidget(self.open_button)
        file_button_layout.addWidget(self.recovery_button)

        scroll_layout.addWidget(file_button_frame, row, 1, 1, 1, alignment=Qt.AlignRight)
        row += 1






        button_frame = QFrame()
        button_frame.setStyleSheet(get_frame_style(True))
        button_layout = QHBoxLayout(button_frame)
        self.start_button = QPushButton("Start Trading")
        self.start_button.setObjectName("startTrading")
        self.start_button.setProperty("class", "standardButton")
        self.start_button.setStyleSheet(get_standard_button_style(True))
        self.start_button.clicked.connect(self.check_and_start_trading)
        # Removed direct start_watching call to prevent duplicate threads - now only called via QTimer in start_nano_trading
        self.stop_button = QPushButton("Stop Trading")
        self.stop_button.setObjectName("stopTrading")
        self.stop_button.setProperty("class", "standardButton")
        self.stop_button.setStyleSheet(get_standard_button_style(True))
        self.stop_button.clicked.connect(self.stop_nano_trading)
        # Connecter le bouton Stop Trading à la méthode stop_watching du PriceWatcher
        self.stop_button.clicked.connect(self.nano_processor.stop_watching)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        scroll_layout.addWidget(button_frame, row, 0, 1, 2)

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
        self.update_button_states()

        return widget

    def save_configuration_to_file(self):
        try:
            # Vérifier que l'application Qt est complètement initialisée
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                self.display_to_status_bar("Error", "Application not fully initialized. Please wait a moment and try again.")
                return

            # Vérifier que tous les widgets nécessaires sont initialisés
            if not hasattr(self, 'nano_trading_exchange_vars') or not self.nano_trading_exchange_vars:
                self.display_to_status_bar("Error", "Configuration interface not ready. Please wait for complete initialization.")
                return

            options = QFileDialog.Options()
            file_name, _ = QFileDialog.getSaveFileName(None, "Save Configuration", "", "Nano Trading Config (*.nta)", options=options)

            if file_name:
                if not file_name.endswith('.nta'):
                    file_name += '.nta'

                # Vérifier que toutes les conditions sont remplies avant de sauvegarder
                if not self.are_all_conditions_fulfilled():
                    self.display_to_status_bar("Error", "Please fulfill all conditions before saving.")
                    return

                # Récupérer toutes les valeurs de la fenêtre de configuration avec protection
                try:
                    config_data = {
                        "exchanges": self._safe_get_checked_exchanges(),
                        "trade_amount": self._safe_get_text(self.trade_amount_entry),
                        "num_trades": self._safe_get_text(self.num_trades_entry),
                        "symbols": {
                            "USDT": self._safe_is_checked(self.usdt_checkbox),
                            "USDC": self._safe_is_checked(self.usdc_checkbox),
                            "BTC": self._safe_is_checked(self.btc_checkbox),
                            "custom_symbol": self._safe_get_text(self.custom_symbol_entry)
                        },
                        "excluded_tokens": self._safe_get_text(self.excluded_tokens_entry),
                        "excluded_end_prefix": self._safe_get_text(self.excluded_end_prefix_entry),
                        "excluded_start_prefix": self._safe_get_text(self.excluded_start_prefix_entry),
                        "search_index_range": {
                            "start": self._safe_get_text(self.search_index_start_entry),
                            "end": self._safe_get_text(self.search_index_end_entry)
                        },
                        "timeframe": self._safe_get_current_text(self.timeframe_combobox),
                        "threshold": self._safe_get_text(self.threshold_entry),
                        "liquidity_filtering": {
                            "min_volume_usdt": self._safe_get_text(self.min_volume_entry) or str(self.min_volume_usdt),
                            "max_spread_pct": self._safe_get_text(self.max_spread_entry) or str(self.max_spread_pct)
                        },
                        "main_action": self._safe_get_current_text(self.main_action_combobox),
                        "timeframes_depth": self._safe_get_checkboxes_dict(self.timeframe_checkboxes),
                        "indicators": self._safe_get_checkboxes_dict(self.indicator_vars),
                        # Persister les réglages d'indicateurs (ajoutés au save manuel .nta)
                        "indicator_settings": self.indicator_settings,
                        # Sérialiser les configurations d'achat/vente avec protection
                        "buy_setup": self._safe_serialize_rows(self.buy_rows, 'buy'),
                        "sell_setup": self._safe_serialize_rows(self.sell_rows, 'sell'),
                        # Sauvegarder les actions des timeframes de profondeur avec protection
                        "timeframes_depth_actions": self._safe_get_depth_actions(),
                        # Sauvegarder les modes de profit
                        "profit_modes": {
                            "average_price": self._safe_is_checked(self.profit_mode_average),
                            "last_price": self._safe_is_checked(self.profit_mode_last),
                            "fixed_profit": self._safe_is_checked(self.profit_mode_fixed),
                            "fixed_profit_amount": self._safe_get_text(self.fixed_profit_entry)
                        },
                        # Sauvegarder les paramètres de stop loss
                        "stop_loss": {
                            "enabled": self._safe_is_checked(self.stop_loss_enabled_radio),
                            "percentage": self._safe_get_text(self.stop_loss_input),
                            "mode": "with_buy_rows" if self._safe_is_checked(self.stop_loss_with_buy_rows_radio) else "without_buy_rows"
                        }
                    }
                except RuntimeError as e:
                    if "internal C++ object" in str(e):
                        self.display_to_status_bar("Error", "Configuration widgets not ready. Please wait for complete initialization and try again.")
                        return
                    else:
                        raise e

                # Sauvegarder sous format JSON dans un fichier .nta
                with open(file_name, 'w') as f:
                    json.dump(config_data, f, indent=4)

                self.display_to_status_bar("Configuration saved", f"Configuration saved to {file_name}")

        except Exception as e:
            self.display_to_status_bar("Error", f"An error occurred while saving: {str(e)}")

    def save_recovery_configuration(self):
        """
        Sauvegarde automatiquement la configuration sous config_recovery.nta
        lors du démarrage du bot.
        """
        try:
            # Vérifier que l'application Qt est complètement initialisée
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                self.display_to_status_bar("Warning", "Cannot save recovery config: Application not initialized.")
                return False

            # Vérifier que tous les widgets nécessaires sont initialisés
            if not hasattr(self, 'nano_trading_exchange_vars') or not self.nano_trading_exchange_vars:
                self.display_to_status_bar("Warning", "Cannot save recovery config: Interface not ready.")
                return False

            # Nom du fichier de récupération
            recovery_file = "config_recovery.nta"

            # Vérifier que toutes les conditions sont remplies
            if not self.are_all_conditions_fulfilled():
                self.display_to_status_bar("Warning", "Cannot save recovery config: Configuration incomplete.")
                return False

            try:
                # Créer les données de configuration
                config_data = {
                    "exchanges": self._safe_get_checked_exchanges(),
                    "trade_amount": self._safe_get_text(self.trade_amount_entry),
                    "num_trades": self._safe_get_text(self.num_trades_entry),
                    "symbols": {
                        "USDT": self._safe_is_checked(self.usdt_checkbox),
                        "USDC": self._safe_is_checked(self.usdc_checkbox),
                        "BTC": self._safe_is_checked(self.btc_checkbox),
                        "custom_symbol": self._safe_get_text(self.custom_symbol_entry)
                    },
                    "excluded_tokens": self._safe_get_text(self.excluded_tokens_entry),
                    "excluded_end_prefix": self._safe_get_text(self.excluded_end_prefix_entry),
                    "excluded_start_prefix": self._safe_get_text(self.excluded_start_prefix_entry),
                    "search_index_range": {
                        "start": self._safe_get_text(self.search_index_start_entry),
                        "end": self._safe_get_text(self.search_index_end_entry)
                    },
                    "timeframe": self._safe_get_current_text(self.timeframe_combobox),
                    "threshold": self._safe_get_text(self.threshold_entry),
                    "liquidity_filtering": {
                        "min_volume_usdt": self._safe_get_text(self.min_volume_entry) or str(self.min_volume_usdt),
                        "max_spread_pct": self._safe_get_text(self.max_spread_entry) or str(self.max_spread_pct)
                    },
                    "main_action": self._safe_get_current_text(self.main_action_combobox),
                    "timeframes_depth": self._safe_get_checkboxes_dict(self.timeframe_checkboxes),
                    "indicators": self._safe_get_checkboxes_dict(self.indicator_vars),
                    # Persister les réglages d'indicateurs
                    "indicator_settings": self.indicator_settings,
                    # Sérialiser les configurations d'achat/vente avec protection
                    "buy_setup": self._safe_serialize_rows(self.buy_rows, 'buy'),
                    "sell_setup": self._safe_serialize_rows(self.sell_rows, 'sell'),
                    # Sauvegarder les actions des timeframes de profondeur avec protection
                    "timeframes_depth_actions": self._safe_get_depth_actions(),
                    # Sauvegarder les modes de profit
                    "profit_modes": {
                        "fixed_profit": self._safe_get_checkbox_state(self.profit_mode_fixed),
                        "last_price": self._safe_get_checkbox_state(self.profit_mode_last),
                        "average_price": not self._safe_get_checkbox_state(self.profit_mode_fixed) and not self._safe_get_checkbox_state(self.profit_mode_last)
                    },
                    # Sauvegarder le montant de profit fixe
                    "fixed_profit_amount": self._safe_get_text(self.fixed_profit_entry) or "0.15",
                    # Sauvegarder les paramètres de stop loss
                    "stop_loss": {
                        "enabled": self._safe_is_checked(self.stop_loss_enabled_radio),
                        "percentage": self._safe_get_text(self.stop_loss_input),
                        "mode": "with_buy_rows" if self._safe_is_checked(self.stop_loss_with_buy_rows_radio) else "without_buy_rows"
                    }
                }
            except RuntimeError as e:
                if "internal C++ object" in str(e):
                    self.display_to_status_bar("Warning", "Cannot save recovery config: Widgets not ready.")
                    return False
                else:
                    raise e

            # Sauvegarder sous format JSON dans le fichier de récupération
            with open(recovery_file, 'w') as f:
                json.dump(config_data, f, indent=4)

            self.display_to_status_bar("Recovery Config", f"Configuration saved to {recovery_file}")
            return True

        except Exception as e:
            self.display_to_status_bar("Warning", f"Failed to save recovery config: {str(e)}")
            return False

    def load_recovery_configuration(self):
        """
        Charge la configuration de récupération depuis config_recovery.nta.
        """
        recovery_file = "config_recovery.nta"

        if not os.path.exists(recovery_file):
            self.display_to_status_bar("Info", "No recovery configuration found.")
            return False

        try:
            # Activer le mode récupération
            self.is_recovery_mode = True
            
            # Charger le fichier de configuration de récupération
            with open(recovery_file, 'r') as f:
                config_data = json.load(f)

            # Vérifier que toutes les conditions nécessaires sont présentes dans le fichier
            if not self.is_configuration_valid(config_data):
                self.display_to_status_bar("Error", "Invalid recovery configuration file.")
                self.is_recovery_mode = False  # Désactiver le mode récupération en cas d'erreur
                return False

            # Restaurer les valeurs dans les widgets
            for exchange, checkbox in self.nano_trading_exchange_vars.items():
                checkbox.setChecked(exchange in config_data["exchanges"])

            self.trade_amount_entry.setText(config_data["trade_amount"])
            self.num_trades_entry.setText(config_data["num_trades"])

            self.usdt_checkbox.setChecked(config_data["symbols"]["USDT"])
            self.usdc_checkbox.setChecked(config_data["symbols"]["USDC"])
            self.btc_checkbox.setChecked(config_data["symbols"]["BTC"])
            self.custom_symbol_entry.setText(config_data["symbols"]["custom_symbol"])

            self.excluded_tokens_entry.setText(config_data["excluded_tokens"])
            self.excluded_end_prefix_entry.setText(config_data["excluded_end_prefix"])
            self.excluded_start_prefix_entry.setText(config_data["excluded_start_prefix"])

            self.search_index_start_entry.setText(config_data["search_index_range"]["start"])
            self.search_index_end_entry.setText(config_data["search_index_range"]["end"])

            self.timeframe_combobox.setCurrentText(config_data["timeframe"])
            self.threshold_entry.setText(config_data["threshold"])

            # Charger les paramètres de filtrage de liquidité
            if "liquidity_filtering" in config_data:
                min_volume_value = config_data["liquidity_filtering"].get("min_volume_usdt", str(self.min_volume_usdt))
                max_spread_value = config_data["liquidity_filtering"].get("max_spread_pct", str(self.max_spread_pct))
                self.min_volume_entry.setText(min_volume_value)
                self.max_spread_entry.setText(max_spread_value)
            else:
                # Utiliser les valeurs par défaut si la section n'existe pas
                self.min_volume_entry.setText(str(self.min_volume_usdt))
                self.max_spread_entry.setText(str(self.max_spread_pct))

            self.main_action_combobox.setCurrentText(config_data["main_action"])

            for tf, checkbox in self.timeframe_checkboxes.items():
                checkbox.setChecked(config_data["timeframes_depth"].get(tf, False))

            # Mettre à jour les comboboxes des actions des timeframes de profondeur
            self.update_timeframe_actions()
            if "timeframes_depth_actions" in config_data:
                for tf, action in config_data["timeframes_depth_actions"].items():
                    if tf in self.depth_action_comboboxes:
                        self.depth_action_comboboxes[tf].setCurrentText(action)

            for indicator, checkbox in self.indicator_vars.items():
                checkbox.setChecked(config_data["indicators"].get(indicator, False))

            # Restaurer les réglages d'indicateurs si présents
            if "indicator_settings" in config_data and isinstance(config_data["indicator_settings"], dict):
                self.indicator_settings = config_data["indicator_settings"]

            # Restaurer les conditions de buy et sell
            self.restore_buy_conditions(config_data["buy_setup"])
            self.restore_sell_conditions(config_data["sell_setup"])

            # Restaurer les modes de profit
            if "profit_modes" in config_data:
                profit_modes = config_data["profit_modes"]
                print(f"🔄 Restoring profit modes: {profit_modes}")

                # D'abord, décocher tous les modes
                if hasattr(self, 'profit_mode_fixed'):
                    self.profit_mode_fixed.setChecked(False)
                if hasattr(self, 'profit_mode_last'):
                    self.profit_mode_last.setChecked(False)
                # Le mode average_price est le mode par défaut (pas de checkbox)

                # Restaurer le mode correct
                if profit_modes.get("fixed_profit", False):
                    if hasattr(self, 'profit_mode_fixed'):
                        self.profit_mode_fixed.setChecked(True)
                        print(f"✅ FIXED PROFIT mode restored")
                elif profit_modes.get("last_price", False):
                    if hasattr(self, 'profit_mode_last'):
                        self.profit_mode_last.setChecked(True)
                        print(f"✅ LAST PRICE mode restored")
                else:
                    # Mode average_price par défaut - aucune checkbox cochée
                    print(f"✅ AVERAGE PRICE mode restored (default)")

                # Restaurer le montant de profit fixe si applicable
                if profit_modes.get("fixed_profit", False) and "fixed_profit_amount" in config_data:
                    if hasattr(self, 'fixed_profit_entry'):
                        self.fixed_profit_entry.setText(str(config_data["fixed_profit_amount"]))
                        print(f"✅ Fixed profit amount restored: {config_data['fixed_profit_amount']} USDT")

                # Appliquer les changements d'interface
                self.on_profit_mode_changed()
                print(f"✅ Profit modes restored successfully")
            else:
                print("⚠️ No profit_modes found in recovery configuration")

            # Restaurer les paramètres de stop loss s'ils existent
            if "stop_loss" in config_data:
                sl = config_data["stop_loss"]
                try:
                    if hasattr(self, 'stop_loss_enabled_radio'):
                        self.stop_loss_enabled_radio.setChecked(bool(sl.get("enabled", False)))
                    if hasattr(self, 'stop_loss_input'):
                        self.stop_loss_input.setText(str(sl.get("percentage", "")))
                    mode = sl.get("mode", "with_buy_rows")
                    if mode == "with_buy_rows" and hasattr(self, 'stop_loss_with_buy_rows_radio'):
                        self.stop_loss_with_buy_rows_radio.setChecked(True)
                    elif mode == "without_buy_rows" and hasattr(self, 'stop_loss_without_buy_rows_radio'):
                        self.stop_loss_without_buy_rows_radio.setChecked(True)
                except RuntimeError:
                    pass

            # Synchroniser unique_traded_pairs avec les trades existants en base pour éviter les doublons
            try:
                existing_trades = self.db.get_all_trades()
                self.unique_traded_pairs = set()
                for trade in existing_trades:
                    symbol = trade.get('symbol', '')
                    if symbol:
                        self.unique_traded_pairs.add(symbol)
                print(f"🔄 Recovery: Synchronized {len(self.unique_traded_pairs)} existing trades: {self.unique_traded_pairs}")
                
                # Charger les trades existants dans l'interface utilisateur (avec nettoyage automatique)
                if hasattr(self, 'dashboard') and hasattr(self.dashboard, 'exchanges_trades_tables'):
                    print("🔄 Recovery: Loading existing trades into UI...")
                    self.dashboard.exchanges_trades_tables.load_existing_trades_from_database(self.db)
                    print("✅ Recovery: Trades loaded successfully into UI")
                        
            except Exception as e:
                print(f"⚠️ Warning: Could not sync existing trades during recovery: {e}")
                import traceback
                print(f"🔍 DEBUG: Full traceback: {traceback.format_exc()}")
                self.unique_traded_pairs = set()
            finally:
                # Désactiver le mode récupération une fois le chargement terminé
                self.is_recovery_mode = False

            # Vérifier que les boutons "Start Trading" et "Stop Trading" sont bien visibles
            self.update_button_states()
            self.display_to_status_bar("Recovery Config", f"Recovery configuration loaded from {recovery_file}")
            return True

        except Exception as e:
            self.display_to_status_bar("Error", f"Failed to load recovery config: {str(e)}")
            return False

    def handle_recovery_configuration(self):
        """
        Gère intelligemment la configuration de récupération :
        - Crée le fichier s'il n'existe pas
        - Compare avec la configuration actuelle s'il existe
        - Met à jour seulement si la configuration a changé
        """
        recovery_file = "config_recovery.nta"

        try:
            # Obtenir la configuration actuelle
            current_config = self._get_current_configuration()
            if not current_config:
                self.display_to_status_bar("Warning", "Cannot get current configuration")
                return False

            # Vérifier si le fichier de récupération existe
            if not os.path.exists(recovery_file):
                # Le fichier n'existe pas, le créer
                self.display_to_status_bar("Info", "Recovery file not found, creating new one...")
                return self._save_recovery_config_data(current_config, recovery_file)

            # Le fichier existe, le comparer avec la configuration actuelle
            try:
                with open(recovery_file, 'r') as f:
                    saved_config = json.load(f)

                # Comparer les configurations
                if self._configurations_are_equal(current_config, saved_config):
                    self.display_to_status_bar("Info", "Recovery configuration is up to date")
                    return True
                else:
                    # Les configurations sont différentes, mettre à jour
                    self.display_to_status_bar("Info", "Configuration changed, updating recovery file...")
                    # Supprimer l'ancien fichier
                    os.remove(recovery_file)
                    # Créer le nouveau
                    return self._save_recovery_config_data(current_config, recovery_file)

            except (json.JSONDecodeError, KeyError) as e:
                # Le fichier de récupération est corrompu, le recréer
                self.display_to_status_bar("Warning", "Recovery file corrupted, recreating...")
                if os.path.exists(recovery_file):
                    os.remove(recovery_file)
                return self._save_recovery_config_data(current_config, recovery_file)

        except Exception as e:
            self.display_to_status_bar("Error", f"Failed to handle recovery config: {str(e)}")
            return False

    def _handle_recovery_configuration_async(self):
        """
        Gère la configuration de récupération de manière asynchrone pour éviter le blocage Linux.
        """
        try:
            recovery_handled = self.handle_recovery_configuration()
            if recovery_handled:
                self.display_to_status_bar("Success", "Recovery configuration handled successfully")
            else:
                self.display_to_status_bar("Warning", "Could not handle recovery configuration")
        except Exception as e:
            self.display_to_status_bar("Error", f"Failed to handle recovery config async: {str(e)}")

    def _get_current_configuration(self):
        """
        Obtient la configuration actuelle sous forme de dictionnaire.
        """
        dark_mode = self.dark_mode

        try:
            # Vérifier que toutes les conditions sont remplies
            if not self.are_all_conditions_fulfilled():
                return None

            config_data = {
                "exchanges": self._safe_get_checked_exchanges(),
                "trade_amount": self._safe_get_text(self.trade_amount_entry),
                "num_trades": self._safe_get_text(self.num_trades_entry),
                "symbols": {
                    "USDT": self._safe_is_checked(self.usdt_checkbox),
                    "USDC": self._safe_is_checked(self.usdc_checkbox),
                    "BTC": self._safe_is_checked(self.btc_checkbox),
                    "custom_symbol": self._safe_get_text(self.custom_symbol_entry)
                },
                "excluded_tokens": self._safe_get_text(self.excluded_tokens_entry),
                "excluded_end_prefix": self._safe_get_text(self.excluded_end_prefix_entry),
                "excluded_start_prefix": self._safe_get_text(self.excluded_start_prefix_entry),
                "search_index_range": {
                    "start": self._safe_get_text(self.search_index_start_entry),
                    "end": self._safe_get_text(self.search_index_end_entry)
                },
                "timeframe": self._safe_get_current_text(self.timeframe_combobox),
                "threshold": self._safe_get_text(self.threshold_entry),
                "liquidity_filtering": {
                    "min_volume_usdt": self._safe_get_text(self.min_volume_entry) or str(self.min_volume_usdt),
                    "max_spread_pct": self._safe_get_text(self.max_spread_entry) or str(self.max_spread_pct)
                },
                "main_action": self._safe_get_current_text(self.main_action_combobox),
                "timeframes_depth": self._safe_get_checkboxes_dict(self.timeframe_checkboxes),
                "indicators": self._safe_get_checkboxes_dict(self.indicator_vars),
                # Persist per-indicator settings
                "indicator_settings": self.indicator_settings,
                "buy_setup": self._safe_serialize_rows(self.buy_rows, 'buy'),
                "sell_setup": self._safe_serialize_rows(self.sell_rows, 'sell'),
                "timeframes_depth_actions": self._safe_get_depth_actions(),
                # Sauvegarder les modes de profit
                "profit_modes": {
                    "average_price": self._safe_is_checked(self.profit_mode_average),
                    "last_price": self._safe_is_checked(self.profit_mode_last),
                    "fixed_profit": self._safe_is_checked(self.profit_mode_fixed),
                    "fixed_profit_amount": self._safe_get_text(self.fixed_profit_entry)
                },
                # Sauvegarder les paramètres de stop loss
                "stop_loss": {
                    "enabled": self._safe_is_checked(self.stop_loss_enabled_radio),
                    "percentage": self._safe_get_text(self.stop_loss_input),
                    "mode": "with_buy_rows" if self._safe_is_checked(self.stop_loss_with_buy_rows_radio) else "without_buy_rows"
                }
            }
            return config_data

        except Exception as e:
            self.display_to_status_bar("Error", f"Failed to get current config: {str(e)}")
            return None

    def open_indicator_settings_dialog(self, indicator_name: str):
        """
        Ouvre une fenêtre de configuration pour un indicateur spécifique.
        Le contenu est un éditeur JSON représentant les paramètres de calcul et les seuils de décision.
        Les valeurs sont persistées dans self.indicator_settings[indicator_name].
        """
        try:
            # Ensure settings store exists
            if not hasattr(self, 'indicator_settings') or not isinstance(getattr(self, 'indicator_settings'), dict):
                self.indicator_settings = {}

            # Merge user settings with defaults inferred from formulas and decision defaults
            current_settings = merge_with_defaults(indicator_name, self.indicator_settings.get(indicator_name, {}))

            # Parent the dialog to a real QWidget to inherit its stylesheet (light/dark)
            parent_widget = None
            try:
                if hasattr(self, 'dashboard') and self.dashboard is not None and hasattr(self.dashboard, 'main_widget'):
                    parent_widget = self.dashboard.main_widget
                else:
                    app = QApplication.instance()
                    if app is not None and app.activeWindow() is not None:
                        parent_widget = app.activeWindow()
            except Exception:
                parent_widget = None
            dialog = QDialog(parent_widget)
            dialog.setObjectName("IndicatorSettingsDialog")
            dialog.setWindowTitle(f"Settings • {indicator_name}")
            dialog.setStyleSheet(get_dialog_style(self.dark_mode))

            # Layout
            vbox = QVBoxLayout(dialog)
            title = QLabel(f"Configure parameters for {indicator_name}")
            title.setStyleSheet("font-weight: bold; margin-bottom: 8px;")
            vbox.addWidget(title)

            tabs = QTabWidget(dialog)

            # Form tab
            form_tab = QWidget()
            form_layout = QFormLayout(form_tab)
            widgets = {}
            schema = get_schema(indicator_name)
            for key, field in schema.items():
                ftype = field.get("type", "str")
                default_val = current_settings.get(key, field.get("default"))
                if ftype == "int":
                    w = QSpinBox(form_tab)
                    w.setRange(int(field.get("min", 0)), int(field.get("max", 100000)))
                    w.setSingleStep(int(field.get("step", 1)))
                    w.setValue(int(default_val))
                elif ftype == "float":
                    w = QDoubleSpinBox(form_tab)
                    w.setRange(float(field.get("min", -10000.0)), float(field.get("max", 10000.0)))
                    w.setSingleStep(float(field.get("step", 0.1)))
                    w.setDecimals(6)
                    w.setValue(float(default_val))
                elif ftype == "bool":
                    w = QCheckBox(form_tab)
                    w.setChecked(bool(default_val))
                elif ftype == "choice":
                    w = QComboBox(form_tab)
                    for c in field.get("choices", []):
                        w.addItem(str(c))
                    idx = w.findText(str(default_val))
                    if idx >= 0:
                        w.setCurrentIndex(idx)
                else:
                    w = QLineEdit(form_tab)
                    w.setText(str(default_val))
                widgets[key] = w
                label = field.get("label", key)
                form_layout.addRow(QLabel(label), w)

            tabs.addTab(form_tab, "Form")

            # JSON tab (advanced)
            json_tab = QWidget()
            json_layout = QVBoxLayout(json_tab)
            editor = QTextEdit(json_tab)
            try:
                pretty = json.dumps(current_settings, indent=2, ensure_ascii=False)
            except Exception:
                pretty = "{}"
            editor.setPlainText(pretty)
            json_layout.addWidget(editor)
            tabs.addTab(json_tab, "Avancé (JSON)")

            vbox.addWidget(tabs)

            # Buttons
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            reset_btn = QPushButton("Reset")
            buttons.addButton(reset_btn, QDialogButtonBox.ResetRole)
            help_btn = QPushButton("?")
            help_btn.setToolTip(f"Open help for {indicator_name}")
            buttons.addButton(help_btn, QDialogButtonBox.HelpRole)
            vbox.addWidget(buttons)

            def on_reset():
                defaults = get_defaults(indicator_name)
                for key, field in schema.items():
                    dv = defaults.get(key, field.get("default"))
                    w = widgets.get(key)
                    if w is None:
                        continue
                    if isinstance(w, QSpinBox):
                        w.setValue(int(dv))
                    elif isinstance(w, QDoubleSpinBox):
                        w.setValue(float(dv))
                    elif isinstance(w, QCheckBox):
                        w.setChecked(bool(dv))
                    elif isinstance(w, QComboBox):
                        idx = w.findText(str(dv))
                        if idx >= 0:
                            w.setCurrentIndex(idx)
                    else:
                        w.setText(str(dv))
                try:
                    pretty_defaults = json.dumps(defaults, indent=2, ensure_ascii=False)
                except Exception:
                    pretty_defaults = "{}"
                editor.setPlainText(pretty_defaults)

            reset_btn.clicked.connect(on_reset)

            def on_help():
                try:
                    from help import create_help_widget
                except Exception as e:
                    QMessageBox.warning(dialog, "Help", f"Unable to load help: {e}")
                    return
                help_dialog = QDialog(dialog)
                help_dialog.setObjectName("IndicatorHelpDialog")
                help_dialog.setWindowTitle(f"Help • {indicator_name}")
                help_layout = QVBoxLayout(help_dialog)
                try:
                    help_widget = create_help_widget(indicator_name)
                except Exception:
                    help_widget = create_help_widget()
                help_widget.setMinimumSize(700, 500)
                help_layout.addWidget(help_widget)
                help_dialog.setAttribute(Qt.WA_StyledBackground, True)
                help_dialog.setAutoFillBackground(True)
                try:
                    help_dialog.setAttribute(Qt.WA_TranslucentBackground, False)
                except Exception:
                    pass
                help_dialog.exec()

            help_btn.clicked.connect(on_help)

            def on_accept():
                if tabs.currentIndex() == 0:
                    collected = {}
                    for key, field in schema.items():
                        w = widgets[key]
                        if isinstance(w, QSpinBox):
                            collected[key] = int(w.value())
                        elif isinstance(w, QDoubleSpinBox):
                            collected[key] = float(w.value())
                        elif isinstance(w, QCheckBox):
                            collected[key] = bool(w.isChecked())
                        elif isinstance(w, QComboBox):
                            collected[key] = w.currentText()
                        else:
                            collected[key] = w.text().strip()
                    ok, errors = validate(indicator_name, collected)
                    if not ok:
                        msg = "\n".join([f"- {k}: {v}" for k, v in errors.items()])
                        QMessageBox.warning(dialog, "Invalid values", f"Please fix the following:\n{msg}")
                        return
                    self.indicator_settings[indicator_name] = collected
                else:
                    text = editor.toPlainText().strip() or "{}"
                    try:
                        parsed = json.loads(text)
                        if not isinstance(parsed, dict):
                            raise ValueError("Indicator settings must be a JSON object")
                    except Exception as e:
                        QMessageBox.warning(dialog, "Invalid JSON", f"Please provide valid JSON.\nError: {e}")
                        return
                    ok, errors = validate(indicator_name, parsed)
                    if not ok:
                        msg = "\n".join([f"- {k}: {v}" for k, v in errors.items()])
                        QMessageBox.warning(dialog, "Invalid values", f"Please fix the following:\n{msg}")
                        return
                    self.indicator_settings[indicator_name] = parsed
                dialog.accept()

            def on_reject():
                dialog.reject()

            buttons.accepted.connect(on_accept)
            buttons.rejected.connect(on_reject)

            # Do not apply a custom stylesheet; inherit current app theme (fixes light mode)
            dialog.exec()
        except Exception as e:
            try:
                self.display_to_status_bar("Error", f"Failed to open settings for {indicator_name}: {e}")
            except Exception:
                print(f"Failed to open settings for {indicator_name}: {e}")

    def select_all_indicators(self):
        """Sélectionne tous les indicateurs."""
        try:
            for indicator, checkbox in self.indicator_vars.items():
                checkbox.setChecked(True)
            print("✅ Tous les indicateurs ont été sélectionnés")
            self.display_to_nano_terminal("NANO|System", "Tous les indicateurs ont été sélectionnés", "info")
        except Exception as e:
            print(f"Erreur lors de la sélection de tous les indicateurs: {e}")
            self.display_to_nano_terminal("NANO|System", f"Erreur lors de la sélection: {e}", "error")

    def deselect_all_indicators(self):
        """Désélectionne tous les indicateurs."""
        try:
            for indicator, checkbox in self.indicator_vars.items():
                checkbox.setChecked(False)
            print("❌ Tous les indicateurs ont été désélectionnés")
            self.display_to_nano_terminal("NANO|System", "Tous les indicateurs ont été désélectionnés", "info")
        except Exception as e:
            print(f"Erreur lors de la désélection de tous les indicateurs: {e}")
            self.display_to_nano_terminal("NANO|System", f"Erreur lors de la désélection: {e}", "error")

    def _save_recovery_config_data(self, config_data, recovery_file):
        """
        Sauvegarde les données de configuration dans le fichier de récupération.
        """
        try:
            with open(recovery_file, 'w') as f:
                json.dump(config_data, f, indent=4)

            self.display_to_status_bar("Recovery Config", f"Configuration saved to {recovery_file}")
            return True

        except Exception as e:
            self.display_to_status_bar("Error", f"Failed to save recovery config: {str(e)}")
            return False

    def _configurations_are_equal(self, config1, config2):
        """
        Compare deux configurations pour voir si elles sont identiques.
        """
        try:
            # Comparer les sections importantes
            important_keys = [
                "exchanges", "trade_amount", "num_trades", "symbols",
                "excluded_tokens", "excluded_end_prefix", "excluded_start_prefix",
                "search_index_range", "timeframe", "threshold", "liquidity_filtering",
                "main_action", "timeframes_depth", "indicators"
            ]

            for key in important_keys:
                if config1.get(key) != config2.get(key):
                    return False

            # Comparer les configurations d'achat et de vente (plus complexe)
            if not self._compare_trading_setups(config1.get("buy_setup", []), config2.get("buy_setup", [])):
                return False

            if not self._compare_trading_setups(config1.get("sell_setup", []), config2.get("sell_setup", [])):
                return False

            return True

        except Exception:
            # En cas d'erreur de comparaison, considérer comme différent
            return False

    def _compare_trading_setups(self, setup1, setup2):
        """
        Compare deux configurations de trading (buy ou sell).
        """
        try:
            if len(setup1) != len(setup2):
                return False

            for i, (item1, item2) in enumerate(zip(setup1, setup2)):
                # Comparer les éléments importants de chaque setup
                if item1.get("percentage") != item2.get("percentage"):
                    return False
                if item1.get("amount") != item2.get("amount"):
                    return False
                if item1.get("sell_percentage") != item2.get("sell_percentage"):
                    return False

            return True

        except Exception:
            return False

    # Méthodes helper sécurisées pour éviter les erreurs "internal C++ object"
    def _safe_get_text(self, widget):
        """Récupère le texte d'un widget de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'text'):
                text = widget.text()
                return text if text else ""
        except RuntimeError:
            pass
        return ""

    def _safe_get_current_text(self, widget):
        """Récupère le texte actuel d'un combobox de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'currentText'):
                return widget.currentText()
        except RuntimeError:
            pass
        return ""

    def _safe_is_checked(self, widget):
        """Vérifie si un checkbox est coché de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'isChecked'):
                return widget.isChecked()
        except RuntimeError:
            pass
        return False

    def _safe_get_checked_exchanges(self):
        """Récupère les exchanges cochés de manière sécurisée."""
        checked_exchanges = []
        try:
            if hasattr(self, 'nano_trading_exchange_vars'):
                for ex, checkbox in self.nano_trading_exchange_vars.items():
                    if self._safe_is_checked(checkbox):
                        checked_exchanges.append(ex)
        except RuntimeError:
            pass
        return checked_exchanges

    def _safe_get_checkboxes_dict(self, checkboxes_dict):
        """Récupère un dictionnaire de checkboxes de manière sécurisée."""
        result = {}
        try:
            if checkboxes_dict:
                for key, checkbox in checkboxes_dict.items():
                    result[key] = self._safe_is_checked(checkbox)
        except RuntimeError:
            pass
        return result

    def _safe_serialize_rows(self, rows, row_type):
        """Sérialise les lignes buy/sell de manière sécurisée."""
        serialized_rows = []
        try:
            if rows:
                for row in rows:
                    if row_type == 'buy':
                        serialized_row = {
                            "percentage_combobox": self._safe_get_current_text(row.get("percentage_combobox")),
                            "amount_entry": self._safe_get_text(row.get("amount_entry")),
                            "price_to_buy": row.get("price_to_buy", 0.0)
                        }
                    else:  # sell
                        serialized_row = {
                            "percentage_combobox": self._safe_get_current_text(row.get("percentage_combobox")),
                            "sell_percentage_entry": self._safe_get_text(row.get("sell_percentage_entry")),
                            "price_to_sell": row.get("price_to_sell", 0.0)
                        }
                    serialized_rows.append(serialized_row)
        except RuntimeError:
            pass
        return serialized_rows

    def _safe_get_depth_actions(self):
        """Récupère les actions de profondeur de manière sécurisée."""
        depth_actions = {}
        try:
            if hasattr(self, 'depth_action_comboboxes') and self.depth_action_comboboxes:
                for timeframe, combobox in self.depth_action_comboboxes.items():
                    depth_actions[timeframe] = self._safe_get_current_text(combobox)
        except RuntimeError:
            pass
        return depth_actions

    def _safe_get_checkbox_state(self, checkbox):
        """Récupère l'état d'une checkbox de manière sécurisée"""
        try:
            if checkbox and hasattr(checkbox, 'isChecked'):
                return checkbox.isChecked()
            return False
        except (RuntimeError, AttributeError):
            return False

    def _safe_set_checked(self, widget, value):
        """Définit l'état coché d'un widget de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'setChecked'):
                widget.setChecked(bool(value))
        except RuntimeError:
            pass  # Widget supprimé

    def _safe_set_text(self, widget, value):
        """Définit le texte d'un widget de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'setText'):
                widget.setText(str(value) if value is not None else "")
        except RuntimeError:
            pass  # Widget supprimé

    def open_configuration_file(self):
        try:
            options = QFileDialog.Options()
            file_name, _ = QFileDialog.getOpenFileName(None, "Open Configuration", "", "Nano Trading Config (*.nta)", options=options)

            if file_name:
                # Charger le fichier de configuration
                with open(file_name, 'r') as f:
                    config_data = json.load(f)

                # Vérifier que toutes les conditions nécessaires sont présentes dans le fichier
                if not self.is_configuration_valid(config_data):
                    self.display_to_status_bar("Error", "Invalid configuration file. Please ensure all required data is present.")
                    return

                # Restaurer les valeurs dans les widgets
                for exchange, checkbox in self.nano_trading_exchange_vars.items():
                    checkbox.setChecked(exchange in config_data["exchanges"])

                self.trade_amount_entry.setText(config_data["trade_amount"])
                self.num_trades_entry.setText(config_data["num_trades"])

                self.usdt_checkbox.setChecked(config_data["symbols"].get("USDT", False))
                self.usdc_checkbox.setChecked(config_data["symbols"].get("USDC", False))
                self.btc_checkbox.setChecked(config_data["symbols"].get("BTC", False))
                self.custom_symbol_entry.setText(config_data["symbols"].get("custom_symbol", ""))

                self.excluded_tokens_entry.setText(config_data["excluded_tokens"])
                self.excluded_end_prefix_entry.setText(config_data["excluded_end_prefix"])
                self.excluded_start_prefix_entry.setText(config_data["excluded_start_prefix"])

                self.search_index_start_entry.setText(config_data["search_index_range"]["start"])
                self.search_index_end_entry.setText(config_data["search_index_range"]["end"])

                self.timeframe_combobox.setCurrentText(config_data["timeframe"])
                self.threshold_entry.setText(config_data["threshold"])

                # Charger les paramètres de filtrage de liquidité
                if "liquidity_filtering" in config_data:
                    min_volume_value = config_data["liquidity_filtering"].get("min_volume_usdt", str(self.min_volume_usdt))
                    max_spread_value = config_data["liquidity_filtering"].get("max_spread_pct", str(self.max_spread_pct))
                    self.min_volume_entry.setText(min_volume_value)
                    self.max_spread_entry.setText(max_spread_value)
                else:
                    # Utiliser les valeurs par défaut si la section n'existe pas
                    self.min_volume_entry.setText(str(self.min_volume_usdt))
                    self.max_spread_entry.setText(str(self.max_spread_pct))

                self.main_action_combobox.setCurrentText(config_data["main_action"])

                for tf, checkbox in self.timeframe_checkboxes.items():
                    checkbox.setChecked(config_data["timeframes_depth"].get(tf, False))

                # Mettre à jour les comboboxes des actions des timeframes de profondeur
                self.update_timeframe_actions()

                # Restaurer les actions pour les timeframes de profondeur
                for timeframe, action in config_data.get("timeframes_depth_actions", {}).items():
                    if timeframe in self.depth_action_comboboxes:
                        combobox = self.depth_action_comboboxes[timeframe]
                        combobox.setCurrentText(action)

                for ind, checkbox in self.indicator_vars.items():
                    checkbox.setChecked(config_data["indicators"].get(ind, False))

                # Restaurer les réglages d'indicateurs s'ils existent dans le fichier .nta
                if "indicator_settings" in config_data and isinstance(config_data["indicator_settings"], dict):
                    self.indicator_settings = config_data["indicator_settings"]

                # Restaurer les paramètres de stop loss s'ils existent
                if "stop_loss" in config_data:
                    sl = config_data["stop_loss"]
                    try:
                        if hasattr(self, 'stop_loss_enabled_radio'):
                            self.stop_loss_enabled_radio.setChecked(bool(sl.get("enabled", False)))
                        if hasattr(self, 'stop_loss_input'):
                            self.stop_loss_input.setText(str(sl.get("percentage", "")))
                        mode = sl.get("mode", "with_buy_rows")
                        if mode == "with_buy_rows" and hasattr(self, 'stop_loss_with_buy_rows_radio'):
                            self.stop_loss_with_buy_rows_radio.setChecked(True)
                        elif mode == "without_buy_rows" and hasattr(self, 'stop_loss_without_buy_rows_radio'):
                            self.stop_loss_without_buy_rows_radio.setChecked(True)
                    except RuntimeError:
                        pass

                # Restaurer les conditions de buy et sell
                self.restore_buy_conditions(config_data["buy_setup"])
                self.restore_sell_conditions(config_data["sell_setup"])

                # Restaurer les modes de profit
                if "profit_modes" in config_data:
                    profit_modes = config_data["profit_modes"]
                    self._safe_set_checked(self.profit_mode_average, profit_modes.get("average_price", True))
                    self._safe_set_checked(self.profit_mode_last, profit_modes.get("last_price", False))
                    self._safe_set_checked(self.profit_mode_fixed, profit_modes.get("fixed_profit", False))
                    self._safe_set_text(self.fixed_profit_entry, profit_modes.get("fixed_profit_amount", "0.15"))

                    # Activer/désactiver le champ Fixed Profit selon le mode
                    if hasattr(self, 'on_profit_mode_changed'):
                        self.on_profit_mode_changed()
                else:
                    # Valeurs par défaut si la section n'existe pas
                    self._safe_set_checked(self.profit_mode_average, True)
                    self._safe_set_checked(self.profit_mode_last, False)
                    self._safe_set_checked(self.profit_mode_fixed, False)
                    self._safe_set_text(self.fixed_profit_entry, "0.15")

                # Restaurer les paramètres de stop loss
                if "stop_loss" in config_data:
                    stop_loss_config = config_data["stop_loss"]
                    self._safe_set_checked(self.stop_loss_enabled_radio, stop_loss_config.get("enabled", False))
                    self._safe_set_text(self.stop_loss_input, stop_loss_config.get("percentage", "0.0"))
                    
                    # Restaurer le mode de stop loss
                    mode = stop_loss_config.get("mode", "with_buy_rows")
                    if mode == "with_buy_rows":
                        self._safe_set_checked(self.stop_loss_with_buy_rows_radio, True)
                        self._safe_set_checked(self.stop_loss_without_buy_rows_radio, False)
                    else:
                        self._safe_set_checked(self.stop_loss_with_buy_rows_radio, False)
                        self._safe_set_checked(self.stop_loss_without_buy_rows_radio, True)
                    
                    # Appliquer les changements de stop loss
                    if hasattr(self, 'on_stop_loss_enabled_changed'):
                        self.on_stop_loss_enabled_changed()
                    print(f"✅ Stop loss configuration restored: enabled={stop_loss_config.get('enabled', False)}, percentage={stop_loss_config.get('percentage', '0.0')}%, mode={mode}")
                else:
                    # Valeurs par défaut pour le stop loss
                    self._safe_set_checked(self.stop_loss_enabled_radio, False)
                    self._safe_set_text(self.stop_loss_input, "0.0")
                    self._safe_set_checked(self.stop_loss_with_buy_rows_radio, True)
                    self._safe_set_checked(self.stop_loss_without_buy_rows_radio, False)

                # Vérifier que les boutons "Start Trading" et "Stop Trading" sont bien visibles
                self.update_button_states()
                self.display_to_status_bar("Configuration loaded", f"Configuration loaded from {file_name}")

        except Exception as e:
            self.display_to_status_bar("Error", f"An error occurred while opening: {str(e)}")


    def are_all_conditions_fulfilled(self):
        # Vérifier que les champs obligatoires sont remplis
        basic_conditions = (
            bool(self.trade_amount_entry.text()) and
            bool(self.num_trades_entry.text()) and
            bool(self.timeframe_combobox.currentText())
        )

        # Vérifier que les widgets de liquidité existent et sont initialisés
        # Ces champs peuvent être vides (utiliseront les valeurs par défaut)
        liquidity_widgets_ready = (
            hasattr(self, 'min_volume_entry') and self.min_volume_entry is not None and
            hasattr(self, 'max_spread_entry') and self.max_spread_entry is not None
        )

        # Valider les valeurs numériques des champs de liquidité s'ils sont remplis
        liquidity_values_valid = self._validate_liquidity_values()

        return basic_conditions and liquidity_widgets_ready and liquidity_values_valid

    def _validate_liquidity_values(self):
        """Valide les valeurs numériques des champs de liquidité."""
        try:
            # Vérifier min_volume_entry
            min_volume_text = self._safe_get_text(self.min_volume_entry)
            if min_volume_text:
                min_volume = float(min_volume_text)
                if min_volume < 0:
                    self.display_to_status_bar("Error", "Minimum volume must be positive")
                    return False

            # Vérifier max_spread_entry
            max_spread_text = self._safe_get_text(self.max_spread_entry)
            if max_spread_text:
                max_spread = float(max_spread_text)
                if max_spread < 0 or max_spread > 100:
                    self.display_to_status_bar("Error", "Maximum spread must be between 0 and 100%")
                    return False

            return True
        except ValueError:
            self.display_to_status_bar("Error", "Invalid numeric values in liquidity fields")
            return False


    def is_configuration_valid(self, config_data):
        # Implémentez ici la logique pour vérifier que le fichier de configuration contient toutes les informations nécessaires
        required_keys = ["exchanges", "trade_amount", "num_trades", "symbols", "timeframe", "threshold"]
        return all(key in config_data for key in required_keys)




    def restore_buy_conditions(self, buy_conditions):
        """
        Restaure les conditions d'achat à partir du fichier de configuration.
        """
        # Vider les conditions actuelles avant de les restaurer
        self.buy_rows.clear()

        # Supprimer les widgets actuels pour éviter les doublons
        layout = self.buy_setup_layout
        for i in reversed(range(layout.count())):
            widget = layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Ajouter chaque condition de la configuration à l'interface
        for condition in buy_conditions:
            self.add_buy_row(layout)  # Ajouter une nouvelle ligne d'achat
            row = self.buy_rows[-1]  # Obtenir la dernière ligne ajoutée

            # Remplir les widgets avec les données du fichier
            # Gérer les deux formats possibles
            if "percentage_combobox" in condition:
                # Format ancien (avec suffixes)
                row["percentage_combobox"].setCurrentText(condition["percentage_combobox"])
                row["amount_entry"].setText(condition["amount_entry"])
                row["price_to_buy"] = condition.get("price_to_buy", 0.0)
            else:
                # Format nouveau (config_recovery.nta)
                percentage = condition.get("percentage", 1.5)
                amount = condition.get("amount", 10)

                # Formater le pourcentage pour correspondre aux valeurs de la combobox
                if percentage == int(percentage):
                    # Si c'est un nombre entier, ne pas afficher .0
                    percentage_text = f"{int(percentage)}%"
                else:
                    # Si c'est un nombre décimal, garder les décimales
                    percentage_text = f"{percentage}%"

                row["percentage_combobox"].setCurrentText(percentage_text)
                row["amount_entry"].setText(str(amount))
                row["price_to_buy"] = 0.0



    def restore_sell_conditions(self, sell_conditions):
        """
        Restaure les conditions de vente à partir du fichier de configuration.
        """
        # Vider les conditions actuelles avant de les restaurer
        self.sell_rows.clear()

        # Supprimer les widgets actuels pour éviter les doublons
        layout = self.sell_setup_layout
        for i in reversed(range(layout.count())):
            widget = layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Ajouter chaque condition de la configuration à l'interface
        for condition in sell_conditions:
            self.add_sell_row(layout)  # Ajouter une nouvelle ligne de vente
            row = self.sell_rows[-1]  # Obtenir la dernière ligne ajoutée

            # Remplir les widgets avec les données du fichier
            # Gérer les deux formats possibles
            if "percentage_combobox" in condition:
                # Format ancien (avec suffixes)
                row["percentage_combobox"].setCurrentText(condition["percentage_combobox"])
                row["sell_percentage_entry"].setText(condition["sell_percentage_entry"])
                row["price_to_sell"] = condition.get("price_to_sell", 0.0)
            else:
                # Format nouveau (config_recovery.nta)
                percentage = condition.get("percentage", 5.0)
                sell_percentage = condition.get("sell_percentage", 100)

                # Formater le pourcentage pour correspondre aux valeurs de la combobox
                if percentage == int(percentage):
                    # Si c'est un nombre entier, ne pas afficher .0
                    percentage_text = f"{int(percentage)}%"
                else:
                    # Si c'est un nombre décimal, garder les décimales
                    percentage_text = f"{percentage}%"

                row["percentage_combobox"].setCurrentText(percentage_text)
                row["sell_percentage_entry"].setText(str(sell_percentage))
                row["price_to_sell"] = 0.0




    def update_timeframe_actions(self):
        self.depth_action_comboboxes = {}

        """Met à jour les comboboxes d'action des timeframes en fonction des cases cochées."""
        # Supprimer les anciens widgets du QGridLayout
        while self.timeframe_action_grid.count():
            item = self.timeframe_action_grid.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Réinitialiser le dictionnaire des comboboxes
        self.depth_action_comboboxes.clear()

        # Réinitialiser les colonnes et les lignes pour le placement des nouveaux widgets
        current_row = 0  # Ligne de départ pour les timeframes
        current_col = 0  # Colonne de départ

        # Créer et ajouter les nouveaux labels et comboboxes pour les timeframes sélectionnés
        for timeframe, checkbox in self.timeframe_checkboxes.items():
            if checkbox.isChecked():
                # Créer un label pour chaque timeframe
                label = QLabel(f"{timeframe} Depth Action:")
                self.timeframe_action_grid.addWidget(label, current_row, current_col)  # Ajouter le label dans la colonne actuelle

                # Créer un combobox pour chaque timeframe
                combobox_depth_action = QComboBox()
                combobox_depth_action.setObjectName("customComboBox")  # Définir l'ID personnalisé
                combobox_depth_action.addItems(["Buy", "Sell", "No Action"])
                combobox_depth_action.setMinimumWidth(80)  # Largeur adaptative
                combobox_depth_action.setStyleSheet(get_combobox_style(True))  # Appliquer le style personnalisé

                # Ajouter le combobox à côté du label
                self.timeframe_action_grid.addWidget(combobox_depth_action, current_row + 1, current_col)  # Ajouter le combobox dans la même colonne

                # Stocker le combobox dans le dictionnaire pour usage ultérieur
                self.depth_action_comboboxes[timeframe] = combobox_depth_action

                # Mise à jour des colonnes et lignes
                current_col += 1  # Avancer d'une colonne
                if current_col >= 6:  # Changer de ligne après 6 comboboxes
                    current_col = 0  # Réinitialiser la colonne
                    current_row += 2  # Passer à la prochaine paire de lignes (une pour les labels, une pour les comboboxes)

        # Actualiser le layout pour appliquer les changements
        self.timeframe_action_grid.update()



    def get_selected_timeframe_actions(self):
        selected_timeframe_actions = []

        # Parcourir tous les timeframes dans depth_action_comboboxes
        for timeframe, combobox in self.depth_action_comboboxes.items():
            # Récupérer l'action sélectionnée dans le combobox
            action = combobox.currentText()

            # Créer une entrée sous forme de dictionnaire pour le timeframe et l'action
            selected_timeframe_actions.append({
                "timeframe_depth_action": timeframe,
                "action": action
            })

        return selected_timeframe_actions




    def update_available_timeframes(self):
        selected_exchanges = [exchange for exchange, checkbox in self.nano_trading_exchange_vars.items() if checkbox.isChecked()]

        if not selected_exchanges:
            # Si aucun échange n'est sélectionné, désactiver tous les timeframes
            for checkbox in self.timeframe_checkboxes.values():
                checkbox.setDisabled(True)
            if self.timeframe_combobox:
                self.timeframe_combobox.clear()  # Vider le combobox principal si aucun exchange sélectionné
            return

        # Ensemble pour stocker les timeframes communs entre tous les échanges sélectionnés
        common_timeframes = None

        # Itérer sur chaque échange sélectionné
        for exchange_name in selected_exchanges:
            exchange = getattr(ccxt, exchange_name)({
                'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
            })

            try:
                # Charger les marchés pour obtenir les informations sur les timeframes
                exchange.load_markets()

                # Récupérer les timeframes disponibles pour cet échange
                available_timeframes = set(exchange.timeframes)

                if common_timeframes is None:
                    # Si c'est le premier échange sélectionné, initialiser les timeframes communs
                    common_timeframes = available_timeframes
                else:
                    # Prendre l'intersection avec les timeframes disponibles des autres échanges
                    common_timeframes &= available_timeframes

            except Exception as e:
                self.update_status.emit("Error", f"Error loading markets for {exchange_name}: {e}")
                return

        # Si common_timeframes est toujours None, cela signifie que quelque chose s'est mal passé
        if common_timeframes is None:
            return

        # Définir l'ordre logique des timeframes
        sorted_timeframes_order = [
            "1s", "5s", "15s", "30s", "45s",
            "1m", "3m", "5m", "15m", "30m",
            "1h", "2h", "4h", "6h", "8h", "12h",
            "1d", "3d", "1w", "1M", "3M"
        ]

        # Tri personnalisé en fonction de cet ordre
        sorted_timeframes = [tf for tf in sorted_timeframes_order if tf in common_timeframes]

        if self.timeframe_combobox:
            self.timeframe_combobox.clear()  # Vider le combobox
            if common_timeframes:
                # Ajouter uniquement les timeframes communs triés par ordre croissant
                self.timeframe_combobox.addItems(sorted_timeframes)

                # Si le timeframe actuellement sélectionné n'est pas dans les timeframes communs, sélectionner le premier
                current_timeframe = self.timeframe_combobox.currentText()
                if current_timeframe not in common_timeframes:
                    self.timeframe_combobox.setCurrentIndex(0)  # Sélectionner le premier timeframe disponible

        # Mettre à jour l'état des checkboxes des timeframes de profondeur
        for timeframe, checkbox in self.timeframe_checkboxes.items():
            if timeframe in common_timeframes:
                checkbox.setDisabled(False)
            else:
                checkbox.setDisabled(True)
                checkbox.setChecked(False)  # Désactiver et décocher si indisponible

        # Même logique pour les timeframes depth
        self.update_depth_timeframes(common_timeframes)
        # Appel de la fonction de mise à jour des comboboxes d'action
        self.update_timeframe_actions()


    def update_depth_timeframes(self, common_timeframes):
        self.timeframe_checkboxes_layout.clear()
        """Met à jour les checkboxes des timeframes de profondeur en fonction des timeframes disponibles."""
        # Ne pas vider les checkboxes existantes
        for checkbox in self.timeframe_checkboxes.values():
            checkbox.setDisabled(True)
            checkbox.setChecked(False)

        # Ajouter uniquement les timeframes de profondeur disponibles dans common_timeframes
        col = 0
        row = 0
        for timeframe in sorted(common_timeframes):
            checkbox = self.timeframe_checkboxes.get(timeframe)
            if checkbox:
                checkbox.setDisabled(False)
                self.timeframe_checkboxes_layout.addWidget(checkbox, row, col)
                row += 1
                if row >= 5:
                    row = 0
                    col += 1



    # Fonction pour mettre à jour le texte du label lorsque la sélection change
    def update_tokens_info_label(self):
        selected_exchanges = [ex for ex, checkbox in self.nano_trading_exchange_vars.items() if checkbox.isChecked()]
        selected_currency = self.get_selected_currency()
        tokens_info_text = self.get_tokens_information(selected_exchanges, selected_currency)
        self.tokens_info_label.setText(tokens_info_text)





    def check_exchange_balance(self, exchange_name):
        exchange_class = getattr(ccxt, exchange_name)
        exchange = exchange_class({
            'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
            'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
            'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
        })
        try:
            balance = exchange.fetch_balance()
            for currency, amount in balance['free'].items():
                if currency == 'USDT':
                    self.usdt_checkbox.setEnabled(amount > 0)
                elif currency == 'USDC':
                    self.usdc_checkbox.setEnabled(amount > 0)
                elif currency == 'BTC':
                    self.btc_checkbox.setEnabled(amount > 0)
            self.update_available_timeframes()

        except Exception as e:
            self.display_to_nano_terminal("NANO|System", f"Error fetching balance for {exchange_name}: Maybe you did not select your currency", "info")


    # fonction pour récupérer les informations des tokens et construire le texte
    def get_tokens_information(self,selected_exchanges, selected_currency):
        token_info_list = []
        for exchange_name in selected_exchanges:
            try:
                exchange = getattr(ccxt, exchange_name)({
                    'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                    'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                    'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
                })
                exchange.load_markets()
                tokens = [symbol for symbol in exchange.symbols if symbol.endswith(f'/{selected_currency}')]
                token_info_list.append(f"{exchange_name} has {len(tokens)} tokens quoted with {selected_currency}")
            except Exception as e:
                print(f"Error loading markets for {exchange_name}: {e}")

        # Combiner toutes les informations en une seule chaîne séparée par des virgules
        combined_info = ', '.join(token_info_list)
        return combined_info


    def stop_nano_trading(self):
        # Mettre à jour la barre de statut pour indiquer l'arrêt
        self.update_enhanced_status_bar("🛑 Stopping", "Stopping...", {}, 0, 0, 0.0)

        if self.trading_worker:
            # Déconnecter les signaux du trading_worker
            try:
                self.trading_worker.update_terminal.disconnect(self.display_to_nano_terminal)
            except RuntimeError:
                pass  # Le signal n'était peut-être pas connecté ou déjà déconnecté

            try:
                self.trading_worker.update_status.disconnect(self.display_to_status_bar)
            except RuntimeError:
                pass

            try:
                self.trading_worker.add_trade_signal.disconnect(self.dashboard.exchanges_trades_tables.add_trade_to_table)
            except RuntimeError:
                pass

            try:
                self.trading_worker.update_exchanges_balances_signal.disconnect(self.update_exchanges_balances)
            except RuntimeError:
                pass

            try:
                self.trading_worker.update_statistics_signal.disconnect(
                    self.dashboard.exchange_symbol_trade_data_widget.update_widget_content
                )
            except RuntimeError:
                pass

            self.trading_worker.stop_trading()
            #self.trading_worker = None

        if self.trading_thread:
            self.trading_thread.quit()
            self.trading_thread.wait()
            #self.trading_thread = None

        # Arrêter également le PriceWatcher
        if self.nano_processor and self.nano_processor.worker:
            try:
                self.nano_processor.worker.update_terminal.disconnect(self.display_to_nano_terminal)
            except RuntimeError:
                pass

            try:
                self.nano_processor.worker.trade_updated_signal.disconnect(
                    self.dashboard.exchanges_trades_tables.update_trade_in_ui
                )
            except RuntimeError:
                pass

            try:
                self.db.trade_removed_from_db_signal.disconnect(
                    self.dashboard.exchanges_trades_tables.remove_trade_from_table
                )
            except RuntimeError:
                pass

            try:
                self.nano_processor.worker.graph_removed_signal.disconnect(
                    self.dashboard.exchange_symbol_trade_data_widget.remove_symbol_from_graph
                )
            except RuntimeError:
                pass

            try:
                self.nano_processor.worker.trade_profit_signal.disconnect(
                    self.dashboard.connected_exchanges_widget.update_profits
                )
            except RuntimeError:
                pass
            if self.nano_processor:
                self.nano_processor.stop_watching()
                #self.nano_processor = None



            # Demander à l'utilisateur s'il souhaite supprimer la base de données
        # Demander à l'utilisateur s'il souhaite supprimer la base de données

        # Ne plus demander de supprimer la DB lors de l'arrêt du trading
        # La DB doit être préservée pour permettre la récupération
        if os.path.exists("nano_trading_bot.db"):
            self.display_to_nano_terminal("NANO|System", "Trading stopped. Database preserved for recovery.", "info")

        self.nano_trading_active = False
        self.update_button_states()

        # Stop the bot timer
        if hasattr(self, 'main_window') and self.main_window:
            self.main_window.stop_bot_timer()
            # Mettre à jour l'état du bouton de récupération de configuration
            self.main_window.update_config_recovery_button_state()





    def is_database_locked(self, db_path):
        """Vérifie si la base de données est verrouillée par un autre processus."""
        try:
            # Essayer d'ouvrir en mode exclusif avec un timeout court
            conn = sqlite3.connect(db_path, timeout=0.1)
            conn.execute("BEGIN IMMEDIATE;")
            conn.rollback()
            conn.close()
            return False  # Pas verrouillée
        except sqlite3.OperationalError:
            return True  # Verrouillée
        except Exception:
            return True  # Erreur = considérer comme verrouillée

    def force_close_database_connections(self, db_path):
        """Force la fermeture de toutes les connexions à la base de données."""
        try:
            import gc

            # Fermer les connexions de notre objet database
            if hasattr(self, 'db') and self.db and hasattr(self.db, 'close_all_connections'):
                self.db.close_all_connections()

            # Forcer le garbage collection pour libérer les connexions
            gc.collect()

            # Essayer de se connecter et fermer immédiatement pour forcer la libération
            try:
                conn = sqlite3.connect(db_path, timeout=1.0)
                conn.close()
            except sqlite3.OperationalError:
                pass  # Base de données peut-être déjà fermée

            print("🔒 Database connections force-closed")
            return True

        except Exception as e:
            print(f"⚠️ Warning: Could not force close database connections: {e}")
            return False

    def delete_database(self):
        """Supprimer le fichier de la base de données avec gestion des verrous."""
        db_path = "nano_trading_bot.db"
        if os.path.exists(db_path):
            try:
                # Vérifier si la base de données est verrouillée
                if self.is_database_locked(db_path):
                    print("🔒 Database is locked by another process")

                    # Arrêter le processor s'il existe
                    if hasattr(self, 'nano_processor') and self.nano_processor:
                        print("⏹️ Stopping trading processor...")
                        self.nano_processor.stop_watching()
                        import time
                        time.sleep(1)  # Attendre que le processor s'arrête

                # Forcer la fermeture des connexions
                print("🔒 Forcing database connections to close...")
                self.force_close_database_connections(db_path)

                # Attendre un peu pour que les processus se terminent
                import time
                time.sleep(0.5)

                # Tenter la suppression avec retry
                max_retries = 5
                for attempt in range(max_retries):
                    try:
                        # Vérifier à nouveau si la DB est verrouillée
                        if not self.is_database_locked(db_path):
                            os.remove(db_path)
                            print(f"✅ Database file {db_path} has been deleted.")
                            return True
                        else:
                            if attempt < max_retries - 1:
                                print(f"🔄 Database still locked, attempt {attempt + 1}/{max_retries}")
                                time.sleep(1)
                            else:
                                print("❌ Database remains locked after all attempts")
                                print("💡 Please stop all trading operations and restart the application")
                                return False

                    except PermissionError as perm_error:
                        if attempt < max_retries - 1:
                            print(f"⚠️ Permission denied, attempt {attempt + 1}/{max_retries}")
                            time.sleep(1)
                        else:
                            print(f"❌ Permission denied after all attempts: {perm_error}")
                            print("💡 Close all applications using the database and try again")
                            return False

            except Exception as e:
                print(f"❌ Failed to delete the database file {db_path}: {e}")
                print("💡 Try stopping all trading operations first")
                return False
        else:
            print(f"Database file {db_path} not found.")
            return True













    def update_button_states(self):
        """Met à jour l'état des boutons 'Start Trading' et 'Stop Trading'."""
        if self.nano_trading_active:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
        else:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)


    @Slot()
    def on_trading_finished(self):
        if self.trading_thread:
            self.trading_thread.quit()
            self.trading_thread.wait()

        self.trading_worker = None
        self.trading_thread = None

        self.display_to_nano_terminal("NANO|System", "Trading process has finished.", "info")
        # Mettre à jour la barre de statut pour indiquer l'arrêt complet
        self.update_enhanced_status_bar("✅ Stopped", "Bot Stopped", {}, 0, 0, 0.0)

        # Mettre à jour l'état du bouton de récupération de configuration
        if hasattr(self, 'main_window') and self.main_window:
            self.main_window.update_config_recovery_button_state()

    def liquidate_all_trades(self, active_trades):
        """Liquide immédiatement tous les trades actifs"""
        liquidated_count = 0
        print(f"[LIQUIDATION] Début de la liquidation pour {len(active_trades)} trades")
        print(f"[LIQUIDATION] Détails des trades: {active_trades}")

        try:
            for trade in active_trades:
                try:
                    # Récupération des détails du trade
                    exchange_name = trade.get('exchange', '').lower()
                    symbol = trade.get('symbol', '')
                    base_token = trade.get('base_token', '')
                    amount_to_trade = float(trade.get('amount_to_trade', 0))

                    if not exchange_name or not symbol or amount_to_trade <= 0:
                        print(f"[LIQUIDATION] Trade invalide - Exchange: {exchange_name}, Symbol: {symbol}, Amount: {amount_to_trade}")
                        continue

                    print(f"[LIQUIDATION] Traitement du trade {symbol} sur {exchange_name}, montant: {amount_to_trade}")
                    
                    # Création de l'instance d'échange
                    exchange = None
                    for exch in self.nano_trading_selected_exchanges:
                        if exch.lower() == exchange_name:
                            exchange_class = getattr(ccxt, exch)
                            exchange = exchange_class({
                                'apiKey': os.getenv(f'{exch.upper()}_API_KEY'),
                                'secret': os.getenv(f'{exch.upper()}_SECRET_KEY'),
                                'password': os.getenv(f'{exch.upper()}_PASSWORD', None),
                                'enableRateLimit': True,
                            })
                            break

                    if not exchange:
                        error_msg = f"[LIQUIDATION] Échange {exchange_name} non trouvé ou non configuré"
                        print(error_msg)
                        self.display_to_nano_terminal("ERREUR LIQUIDATION", error_msg, "error")
                        continue

                    # Récupération du prix actuel
                    ticker = exchange.fetch_ticker(symbol)
                    current_price = ticker['last']

                    # Vérification du solde réel sur l'exchange
                    balance = exchange.fetch_balance()
                    available_balance = balance.get(base_token, {}).get('free', 0)
                    
                    print(f"[LIQUIDATION] Solde disponible pour {base_token}: {available_balance}")
                    print(f"[LIQUIDATION] Montant à vendre selon DB: {amount_to_trade}")
                    
                    # Utiliser le minimum entre le solde disponible et le montant en DB
                    actual_amount_to_sell = min(float(available_balance), float(amount_to_trade))
                    
                    if actual_amount_to_sell <= 0:
                        print(f"[LIQUIDATION] Aucun solde disponible pour {base_token} sur {exchange_name}")
                        continue
                    
                    # Création de l'ordre de vente avec le montant réel
                    print(f"[LIQUIDATION] Tentative de vente de {actual_amount_to_sell} {base_token} sur {exchange_name}")
                    order = exchange.create_market_sell_order(symbol, actual_amount_to_sell)
                    print(f"[LIQUIDATION] Ordre de vente exécuté: {order}")
                    
                    # Mettre à jour amount_to_trade avec le montant réellement vendu
                    amount_to_trade = actual_amount_to_sell

                    if order:
                        # Calcul du profit/pertes
                        buy_price = float(trade.get('buy_price', current_price))
                        sell_revenue = amount_to_trade * current_price
                        buy_cost = amount_to_trade * buy_price
                        profit = sell_revenue - buy_cost

                        # Mise à jour du statut du trade dans la base de données
                        self.db.update_trade_status(
                            trade['id'],
                            'completed',
                            current_price,
                            profit,
                            'immediate_liquidation'
                        )

                        liquidated_count += 1
                        print(f"[LIQUIDATION] Liquidé {symbol}: {amount_to_trade} au prix {current_price} (Profit: {profit:.4f})")
                        
                        # Mise à jour du terminal
                        self.display_to_nano_terminal(
                            "Liquidation",
                            f"🔥 Liquidé {symbol}: {amount_to_trade:.6f} {base_token} à ${current_price:.4f}",
                            "warning"
                        )
                
                except Exception as trade_error:
                    error_msg = f"[LIQUIDATION] Erreur lors de la liquidation du trade {trade.get('id', 'inconnu')}: {str(trade_error)}"
                    print(error_msg)
                    self.display_to_nano_terminal(
                        "ERREUR LIQUIDATION",
                        f"❌ Échec de la liquidation: {str(trade_error)}",
                        "error"
                    )
                    continue

        except Exception as e:
            error_msg = f"[LIQUIDATION] Erreur critique lors de la liquidation: {str(e)}"
            print(error_msg)
            self.display_to_nano_terminal(
                "ERREUR CRITIQUE",
                f"❌ Erreur critique lors de la liquidation: {str(e)}",
                "error"
            )
            raise e
        
        # Résumé final
        summary_msg = f"✅ Liquidation terminée: {liquidated_count} trades liquidés avec succès"
        print(f"[LIQUIDATION] {summary_msg}")
        self.display_to_nano_terminal(
            "Liquidation terminée",
            summary_msg,
            "info" if liquidated_count > 0 else "warning"
        )
        
        return liquidated_count

    def on_application_exit(self):
        self.stop_nano_trading()
        if self.nano_processor:
            self.nano_processor.stop_watching()
        #self.nano_processor.stop_all_threads()  # Arrêter tous les threads du NanoProcessor


    def add_buy_row(self, layout):
        frame = QFrame()
        frame.setStyleSheet(get_frame_style(True))
        row_layout = QHBoxLayout(frame)

        label = QLabel("If the price falls to:")
        row_layout.addWidget(label)

        combobox = QComboBox()
        combobox.setObjectName("customComboBox")
        combobox.addItems(["1.5%", "3%", "4.5%",  "5.4%", "8.1%", "11.7%", "15.3%", "18.1%", "21.6%", "24.3%", "27%", "30.6%", "33.3%", "36%", "39.6%", "42.3%", "45%", "48.6%", "51.3%", "54%", "57.6%", "60.3%", "63%", "66.6%", "69.3%", "72%", "75.6%", "78.3%", "81%", "84.6%", "87.3%", "90%", "93.6%", "96.3%", "100%"])
        combobox.setStyleSheet(get_combobox_style(True))
        row_layout.addWidget(combobox)

        label_amount = QLabel("Amount (USDT):")
        row_layout.addWidget(label_amount)

        amount_entry = QLineEdit()
        amount_entry.setPlaceholderText("Enter amount in USDT (required)")
        amount_entry.setObjectName("customLineEdit")
        amount_entry.setStyleSheet(get_text_area_style(True))
        row_layout.addWidget(amount_entry)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(2)

        add_button = QPushButton()
        plus_icon_path = recolor_icon(os.path.join("icons", "plus.png"), (130, 110, 140), "addBuyRowButton-dark.png")
        add_button.setIcon(QIcon(plus_icon_path))
        add_button.setObjectName("addBuyRowButton")
        add_button.setToolTip("Add Buy Order Row")
        add_button.clicked.connect(lambda: self.add_buy_row(layout))
        button_layout.addWidget(add_button)

        remove_button = QPushButton()
        minus_icon_path = recolor_icon(os.path.join("icons", "minus.png"), (130, 110, 140), "removeBuyRowButton-dark.png")
        remove_button.setIcon(QIcon(minus_icon_path))
        remove_button.setObjectName("removeBuyRowButton")
        remove_button.setToolTip("Remove Buy Order Row")
        remove_button.clicked.connect(lambda: layout.removeWidget(frame) or frame.deleteLater())
        button_layout.addWidget(remove_button)

        row_layout.addLayout(button_layout)
        layout.addWidget(frame)
        # Ajouter une nouvelle ligne d'achat
        self.buy_rows.append({
            "percentage_combobox": combobox,  # Ceci est un QComboBox
            "amount_entry": amount_entry,     # Ceci est un QLineEdit
            "price_to_buy": 0.0               # Ceci est un float pour stocker le prix
        })






        self.update_remove_button_state(layout, remove_button)
        #self.check_conditions_filled()  # Vérifier que les conditions sont remplies




    def add_sell_row(self, layout):
        frame = QFrame()
        frame.setStyleSheet(get_frame_style(True))
        row_layout = QHBoxLayout(frame)

        label = QLabel("If the price rises to:")
        row_layout.addWidget(label)

        combobox = QComboBox()
        combobox.setObjectName("customComboBox")
        combobox.addItems(["1.5%", "3%", "4.5%", "6%", "9%", "10%", "15%", "24%", "39%", "63%", "102%"])
        combobox.setStyleSheet(get_combobox_style(True))
        row_layout.addWidget(combobox)

        label_percentage = QLabel("Sell Percentage:")
        row_layout.addWidget(label_percentage)

        percentage_entry = QLineEdit()
        percentage_entry.setPlaceholderText("Enter percentage to sell")
        percentage_entry.setObjectName("customLineEdit")
        percentage_entry.setStyleSheet(get_text_area_style(True))
        row_layout.addWidget(percentage_entry)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(2)

        add_button = QPushButton()
        plus_icon_path = recolor_icon(os.path.join("icons", "plus.png"), (130, 110, 140), "addSellRowButton-dark.png")
        add_button.setIcon(QIcon(plus_icon_path))
        add_button.setObjectName("addSellRowButton")
        add_button.setToolTip("Add Sell Order Row")
        add_button.clicked.connect(lambda: self.add_sell_row(layout))
        button_layout.addWidget(add_button)

        remove_button = QPushButton()
        minus_icon_path = recolor_icon(os.path.join("icons", "minus.png"), (130, 110, 140), "removeSellRowButton-dark.png")
        remove_button.setIcon(QIcon(minus_icon_path))
        remove_button.setObjectName("removeSellRowButton")
        remove_button.setToolTip("Remove Sell Order Row")
        remove_button.clicked.connect(lambda: layout.removeWidget(frame) or frame.deleteLater())
        button_layout.addWidget(remove_button)

        row_layout.addLayout(button_layout)
        layout.addWidget(frame)
        # Récupérer les valeurs du combobox et du QLineEdit
        percentage_value = combobox.currentText()
        sell_percentage_value = percentage_entry.text()

        # Ajouter une nouvelle ligne de vente
        self.sell_rows.append({
            "percentage_combobox": combobox,      # Ceci est un QComboBox
            "sell_percentage_entry": percentage_entry,  # Ceci est un QLineEdit
            "price_to_sell": 0.0,                 # Ceci est un float pour stocker le prix
            "add_button": add_button,             # Bouton d'ajout
            "remove_button": remove_button        # Bouton de suppression
        })






        self.update_remove_button_state(layout, remove_button)
        #self.check_conditions_filled()  # Vérifier que les conditions sont remplies



    def get_buy_conditions(self, symbol, exchange):
        # Récupérer les conditions d'achat pour un symbole et un échange spécifiques
        return [row for row in self.buy_rows if row.get("symbol") == symbol and row.get("exchange") == exchange]



    def get_sell_conditions(self, symbol, exchange):
        # Récupérer les conditions de vente pour un symbole et un échange spécifiques
        return [row for row in self.sell_rows if row.get("symbol") == symbol and row.get("exchange") == exchange]






    def check_conditions_filled(self):
        # Vérifier qu'au moins une condition d'achat est remplie (toujours nécessaire)
        buy_filled = False
        sell_filled = False

        # Vérifier les conditions d'achat avec protection contre les widgets supprimés
        for row in self.buy_rows:
            try:
                if (row.get('amount_entry') and row['amount_entry'] is not None and
                    row.get('percentage_combobox') and row['percentage_combobox'] is not None):

                    amount_text = row['amount_entry'].text().strip()
                    percentage_text = row['percentage_combobox'].currentText()

                    # Une condition d'achat est valide si elle a un pourcentage ET un montant en USDT
                    if amount_text and percentage_text:
                        buy_filled = True
                        break
            except RuntimeError:
                # Widget supprimé, ignorer cette ligne
                continue
            except Exception as e:
                print(f"Error checking buy condition: {str(e)}")
                self.display_to_nano_terminal("NANO|System", f"Error checking buy condition: {str(e)}", "error")
                continue

        # Vérifier le mode de profit pour déterminer si on a besoin de conditions de vente manuelles
        profit_mode = self.get_profit_mode()

        if profit_mode == "fixed_profit":
            # Mode Fixed Profit : les conditions de vente sont calculées automatiquement
            # Vérifier seulement que le montant Fixed Profit est configuré
            try:
                fixed_profit_amount = self.get_fixed_profit_amount()
                if fixed_profit_amount > 0:
                    sell_filled = True
                    print(f"Mode Fixed Profit détecté: profit fixe = {fixed_profit_amount} USDT")
                    self.display_to_nano_terminal("NANO|System", f"Fixed Profit = {fixed_profit_amount} USDT", "info")
                else:
                    print("Mode Fixed Profit: montant de profit fixe invalide")
                    self.display_to_nano_terminal("NANO|System", "Fixed Profit: Invalid fixed profit amount", "error")
            except Exception as e:
                print(f"Erreur vérification Fixed Profit: {e}")
                self.display_to_nano_terminal("NANO|System", f"Verification Error Fixed Profit: {e}", "error")
        else:
            # Modes Average/Last Price : vérifier les conditions de vente manuelles
            for row in self.sell_rows:
                try:
                    if (row.get('sell_percentage_entry') and row['sell_percentage_entry'] is not None and
                        row.get('percentage_combobox') and row['percentage_combobox'] is not None):

                        sell_percentage_text = row['sell_percentage_entry'].text().strip()
                        percentage_text = row['percentage_combobox'].currentText()

                        if sell_percentage_text and percentage_text:
                            sell_filled = True
                            break
                except RuntimeError:
                    # Widget supprimé, ignorer cette ligne
                    continue
                except Exception as e:
                    print(f"Error checking sell condition: {str(e)}")
                    self.display_to_nano_terminal("NANO|System", f"Error checking sell condition: {str(e)}", "error")
                    continue

        # Debug: Afficher les entrées actuelles (avec protection contre les widgets supprimés)
        print("Conditions d'achat actuelles:")
        for i, row in enumerate(self.buy_rows):
            try:
                amount_text = row['amount_entry'].text() if 'amount_entry' in row and row['amount_entry'] is not None else 'None'
                percentage_text = row['percentage_combobox'].currentText() if 'percentage_combobox' in row and row['percentage_combobox'] is not None else 'None'
                print(f"  Row {i}: Montant: {amount_text}, Pourcentage: {percentage_text}")
                self.display_to_nano_terminal("NANO|System", f"Row {i}: Montant: {amount_text}, Pourcentage: {percentage_text}", "info")
            except RuntimeError as e:
                print(f"  Row {i}: Widget deleted - {str(e)}")
                self.display_to_nano_terminal("NANO|System", f"Row {i}: Widget deleted - {str(e)}", "error")
            except Exception as e:
                print(f"  Row {i}: Error accessing widget - {str(e)}")
                self.display_to_nano_terminal("NANO|System", f"Row {i}: Error accessing widget - {str(e)}", "error")

        print("Conditions de vente actuelles:")

        # Vérifier le mode de profit
        profit_mode = self.get_profit_mode()

        if profit_mode == "fixed_profit":
            # Mode Fixed Profit : afficher les valeurs calculées
            try:
                fixed_profit_usdt = self.get_fixed_profit_amount()
                print(f"  Mode: Fixed Profit ({fixed_profit_usdt} USDT)")
                print(f"  Row 0: Pourcentage: 100, Prix calculé automatiquement selon le profit fixe")
                print(f"  Note: Les prix de vente sont calculés dynamiquement selon le prix d'achat")
            except Exception as e:
                print(f"  Erreur affichage Fixed Profit: {e}")
                print(f"  Mode: Fixed Profit (montant non configuré)")
                print(f"  Note: Les prix de vente seront calculés dynamiquement selon le prix d'achat")
        else:
            # Modes Average et Last Price : affichage normal
            for i, row in enumerate(self.sell_rows):
                try:
                    sell_percentage_text = row['sell_percentage_entry'].text() if 'sell_percentage_entry' in row and row['sell_percentage_entry'] is not None else 'None'
                    percentage_text = row['percentage_combobox'].currentText() if 'percentage_combobox' in row and row['percentage_combobox'] is not None else 'None'
                    print(f"  Row {i}: Pourcentage: {sell_percentage_text}, Pourcentage Vente: {percentage_text}")
                except RuntimeError as e:
                    print(f"  Row {i}: Widget deleted - {str(e)}")
                except Exception as e:
                    print(f"  Row {i}: Error accessing widget - {str(e)}")

        if not buy_filled or not sell_filled:
            # Message d'erreur adapté selon le mode
            if not buy_filled:
                error_msg = "You must fulfill at least one purchase condition before starting the bot."
            elif profit_mode == "fixed_profit":
                error_msg = "Fixed Profit mode: You must set a valid profit amount (greater than 0) before starting the bot."
            else:
                error_msg = "You must fulfill at least one sale condition before starting the bot."

            print(error_msg)
            # Fenêtre d'avertissement pour l'utilisateur
            QMessageBox.warning(None, "Missing Condition", error_msg)
            return False

        return True










    def show_error_message(self, message):
        """
        Affiche une fenêtre pop-up pour informer l'utilisateur.
        """
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Warning)
        msg.setText(message)
        msg.setWindowTitle("Trading Bot Error")
        msg.exec()



    def update_remove_button_state(self, layout, remove_button):
        if layout.count() > 1:
            remove_button.setEnabled(True)
        else:
            remove_button.setEnabled(False)

    def get_selected_currency(self):
        # Defensive approach: check if widgets exist before calling isChecked()
        usdt_cb = getattr(self, 'usdt_checkbox', None)
        usdc_cb = getattr(self, 'usdc_checkbox', None)
        btc_cb = getattr(self, 'btc_checkbox', None)
        custom_entry = getattr(self, 'custom_symbol_entry', None)
        
        if usdt_cb is not None and usdt_cb.isChecked():
            return 'USDT'
        elif usdc_cb is not None and usdc_cb.isChecked():
            return 'USDC'
        elif btc_cb is not None and btc_cb.isChecked():
            return 'BTC'
        elif custom_entry is not None:
            custom_symbol = custom_entry.text().upper()
            if custom_symbol:
                return custom_symbol  # Return the actual custom symbol
        # Fallback when UI not initialized yet
        return 'USDT'






    def on_currency_selected(self):
        """
        Méthode appelée lorsque l'utilisateur sélectionne une devise.
        """
        self.selected_currency = self.get_selected_currency()
        if self.selected_currency:
            # Émettre un signal indiquant que la devise a été sélectionnée
            self.currency_selected_signal.emit()





    def add_unique_trade_pair(self, pair):
        if pair not in self.unique_traded_pairs:
            self.unique_traded_pairs.add(pair)

            return True
        return False



    def remove_unique_trade_pair(self, pair):
        self.unique_traded_pairs.discard(pair)



    def initialize_timeframe_checkboxes(self):
        all_timeframes = [
            "1s", "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M", "3M"
        ]
        self.timeframe_checkboxes = {}
        row, col = 0, 0
        grid_layout = self.timeframe_checkboxes_frame.layout()

        for timeframe in all_timeframes:
            checkbox = QCheckBox(timeframe, parent=self.timeframe_checkboxes_frame)
            grid_layout.addWidget(checkbox, row, col)
            self.timeframe_checkboxes[timeframe] = checkbox

            row += 1
            if row >= 5:
                row = 0
                col += 1

                    # Appel initial pour s'assurer que les timeframes sont configurés au début





    def filter_liquid_symbols(self, exchange, symbols, min_volume_usdt=1000000, max_spread_pct=0.1):
        """
        Filtre les symboles basés sur le volume et le spread pour éviter les shitcoins illiquides.
        """
        liquid_symbols = []

        for symbol in symbols:
            try:
                # Récupérer les données du ticker
                ticker = exchange.fetch_ticker(symbol)
                volume = ticker.get('quoteVolume')  # Volume en quote currency (ex: USDT)

                if volume is None or volume < min_volume_usdt:
                    continue

                # Récupérer le carnet d'ordres pour calculer le spread
                order_book = exchange.fetch_order_book(symbol, limit=5)
                bids = order_book.get('bids', [])
                asks = order_book.get('asks', [])

                if not bids or not asks:
                    continue

                best_bid = bids[0][0]
                best_ask = asks[0][0]
                spread_pct = (best_ask - best_bid) / best_ask * 100

                # Vérifier si le symbole respecte les critères de liquidité
                if spread_pct <= max_spread_pct:
                    liquid_symbols.append({
                        'symbol': symbol,
                        'volume_24h': round(volume, 2),
                        'spread_pct': round(spread_pct, 3)
                    })
                    self.display_to_nano_terminal("NANO|A-2",
                        f"{symbol}: Volume={volume:,.0f} USDT, Spread={spread_pct:.3f}%",
                        "info")
                else:
                    self.display_to_nano_terminal("NANO|A-2",
                        f"{symbol} rejected: Spread={spread_pct:.3f}% > {max_spread_pct}%",
                        "warning")

            except Exception as e:
                self.display_to_nano_terminal("NANO|System", f"{symbol}: {str(e)}", "error")
                continue

        return [item['symbol'] for item in liquid_symbols]

    def fetch_data(self, exchange, symbol, timeframe):
        try:
            exchange_name = getattr(exchange, 'name', None)
            if exchange_name is None:
                return None

            data = exchange.fetch_ohlcv(symbol, timeframe)
            if data is None or len(data) == 0:
                return None

            df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            return df
        except Exception as e:
            print(f"Error while retrieving data: {e}")
            return None

    def calculate_indicators(self, df):
        for category, indics in indicators.items():
            for indicator, formula in indics.items():
                # Apply user settings to indicator formula, if provided
                settings = {}
                try:
                    if hasattr(self, 'indicator_settings') and isinstance(self.indicator_settings, dict):
                        settings = self.indicator_settings.get(indicator, {}) or {}
                except Exception:
                    settings = {}

                adjusted = self._apply_indicator_parameters(formula, settings)
                # Execute in explicit context where df, ta, pd exist
                try:
                    # Track columns to sanitize only newly created ones
                    _prev_cols = set(df.columns)
                    # Use a single shared globals/locals exec environment including builtins.
                    # This avoids odd scoping behavior across platforms and ensures objects
                    # referenced in the executed code share the same namespace.
                    _exec_env = {"ta": ta, "pd": pd, "df": df, "__builtins__": __builtins__}
                    exec(adjusted, _exec_env, _exec_env)

                    # Sanitize newly created columns to avoid +/-Inf propagating to C-extensions/Qt
                    try:
                        import numpy as _np
                        _new_cols = [c for c in df.columns if c not in _prev_cols]
                        for _c in _new_cols:
                            s = df[_c]
                            # Only attempt on numeric dtype
                            if _np.issubdtype(s.dtype, _np.number):
                                s.replace([_np.inf, -_np.inf], _np.nan, inplace=True)
                    except Exception:
                        pass
                except Exception as e:
                    import traceback as _nt_tb
                    tb = _nt_tb.format_exc()
                    # Emit detailed diagnostics to help identify invalid indicator formulas/settings
                    try:
                        self.update_terminal.emit("NANO|System", f"Error in {indicator}: {e}", "error")
                        self.update_status.emit("Error", f"Indicator '{indicator}' failed during calculation. Check its settings.\n{tb}")
                    except Exception:
                        pass
                    continue

    def evaluate_market_and_trade(self, df, selected_indicators, layout, threshold, timeframe, symbol):
        # S'assurer de ne pas dépasser le nombre maximum de trades
        self.num_trades = int(self.num_trades_entry.text())
        if len(self.unique_traded_pairs) >= self.num_trades:
            self.display_to_nano_terminal("NANO|P", "Transaction limit reached. No new market assessment will be carried out.", "info")
            return None, 0  # Ignorer les évaluations supplémentaires
        # Évaluer le marché avec les indicateurs sélectionnés
        action, score = self.evaluate_market(df, selected_indicators, threshold, symbol, timeframe)

        # Retourner l'action et le score pour qu'ils soient traités ailleurs
        return action, score

    def _apply_indicator_parameters(self, formula: str, settings: dict) -> str:
        """
        Replace known parameter assignments in an indicator formula string with values from settings.
        For example, if settings = {"window": 10, "window_slow": 26}, it replaces occurrences like
        "window=14" -> "window=10", "window_slow=21" -> "window_slow=26".
        Only numeric (int/float) values are injected.
        """
        if not settings:
            return formula
        adjusted = formula
        for key, val in settings.items():
            try:
                # Only handle numeric values. Accept ints/floats directly, or strings that can be parsed as float
                _is_number = False
                _num_val = None
                if isinstance(val, (int, float)):
                    _is_number = True
                    _num_val = val
                elif isinstance(val, str):
                    try:
                        _num_val = float(val)
                        _is_number = True
                    except Exception:
                        _is_number = False
                if _is_number:
                    # regex: key\s*=\s*number (accept optional minus sign)
                    pattern = rf"({re.escape(key)}\s*=\s*)(-?[0-9]*\.?[0-9]+)"
                    adjusted = re.sub(pattern, rf"\\1{_num_val}", adjusted)
            except Exception:
                continue
        return adjusted


    def _indicator_decision(self, df, indicator, selected_indicators):
        """
        Return (decision, debug_msg) where decision in {"buy", "sell", None} for a single indicator.
        Pulls thresholds from self.indicator_settings with safe defaults via indicator_settings module.
        """
        try:
            import math
            
            def _safe_float(val, default=0.0):
                """Convert to float, return default if NaN/Inf/None"""
                try:
                    if val is None:
                        return default
                    f_val = float(val)
                    if not math.isfinite(f_val):
                        return default
                    return f_val
                except (ValueError, TypeError, OverflowError):
                    return default
            # Merge with defaults if available
            try:
                from indicator_settings import merge_with_defaults
                settings = merge_with_defaults(indicator, getattr(self, 'indicator_settings', {}).get(indicator, {}))
            except Exception:
                settings = (getattr(self, 'indicator_settings', {}) or {}).get(indicator, {})

            price = df['close'].iloc[-1] if 'close' in df.columns else None

            # Moving averages with percentage distance thresholds
            if indicator in ('SMA', 'EMA', 'WMA') and indicator in df.columns:
                ma = _safe_float(df[indicator].iloc[-1])
                if ma == 0 or ma is None:
                    return None, f"{indicator} undefined"
                price_safe = _safe_float(price)
                if price_safe == 0:
                    return None, f"{indicator} price undefined"
                dist = (price_safe - ma) / ma * 100.0
                if not math.isfinite(dist):
                    return None, f"{indicator} distance invalid"
                up_thr = _safe_float(settings.get('buy_threshold_pct_above', 0.3 if indicator=='EMA' else 0.4 if indicator=='WMA' else 0.5))
                down_thr = _safe_float(settings.get('sell_threshold_pct_below', -0.3 if indicator=='EMA' else -0.4 if indicator=='WMA' else -0.5))
                if dist > up_thr:
                    return 'buy', f"{indicator} Buy - Distance: {dist:.2f}% (thr {up_thr})"
                if dist < down_thr:
                    return 'sell', f"{indicator} Sell - Distance: {dist:.2f}% (thr {down_thr})"
                return None, f"{indicator} Neutral ({dist:.2f}%)"

            # RSI thresholds
            if indicator == 'RSI' and 'RSI' in df.columns:
                rsi = _safe_float(df['RSI'].iloc[-1])
                buy_below = _safe_float(settings.get('buy_below', 30))
                sell_above = _safe_float(settings.get('sell_above', 70))
                if rsi < buy_below:
                    return 'buy', f"RSI Buy ({rsi:.2f} < {buy_below})"
                if rsi > sell_above:
                    return 'sell', f"RSI Sell ({rsi:.2f} > {sell_above})"
                return None, f"RSI Neutral ({rsi:.2f})"

            # MACD vs signal
            if indicator in ('MACD', 'MACD_signal') and 'MACD' in df.columns and 'MACD_signal' in df.columns:
                macd = _safe_float(df['MACD'].iloc[-1])
                sig = _safe_float(df['MACD_signal'].iloc[-1])
                rule = settings.get('cross_rule', 'bullish')
                if macd > sig:
                    return ('buy' if rule == 'bullish' else None), f"MACD>Signal ({macd:.4f}>{sig:.4f})"
                if macd < sig:
                    return ('sell' if rule == 'bullish' else None), f"MACD<Signal ({macd:.4f}<{sig:.4f})"
                return None, "MACD Neutral"

            # KST vs signal
            if indicator in ('KST',) and 'KST' in df.columns and 'KST_signal' in df.columns and 'KST_signal' in selected_indicators:
                kst = _safe_float(df['KST'].iloc[-1])
                ks = _safe_float(df['KST_signal'].iloc[-1])
                if kst > ks:
                    return 'buy', f"KST>Signal"
                if kst < ks:
                    return 'sell', f"KST<Signal"
                return None, "KST Neutral"

            # PVO vs signal
            if indicator in ('PVO',) and 'PVO' in df.columns and 'PVO_signal' in df.columns and 'PVO_signal' in selected_indicators:
                pvo = _safe_float(df['PVO'].iloc[-1])
                pvo_sig = _safe_float(df['PVO_signal'].iloc[-1])
                rule = settings.get('cross_rule', 'bullish')
                if pvo > pvo_sig:
                    return ('buy' if rule == 'bullish' else None), f"PVO>Signal ({pvo:.4f}>{pvo_sig:.4f})"
                if pvo < pvo_sig:
                    return ('sell' if rule == 'bullish' else None), f"PVO<Signal ({pvo:.4f}<{pvo_sig:.4f})"
                return None, "PVO Neutral"

            # Vortex pair
            if indicator == 'Vortex_pos' and 'Vortex_pos' in df.columns and 'Vortex_neg' in df.columns and 'Vortex_neg' in selected_indicators:
                vpos = _safe_float(df['Vortex_pos'].iloc[-1])
                vneg = _safe_float(df['Vortex_neg'].iloc[-1])
                if vpos > vneg:
                    return 'buy', 'VORTEX Buy'
                if vpos < vneg:
                    return 'sell', 'VORTEX Sell'
                return None, 'VORTEX Neutral'

            # Ichimoku A/B
            if indicator == 'Ichimoku_A' and 'Ichimoku_A' in df.columns and 'Ichimoku_B' in df.columns and 'Ichimoku_B' in selected_indicators:
                if df['Ichimoku_A'].iloc[-1] > df['Ichimoku_B'].iloc[-1]:
                    return 'buy', 'ICHIMOKU A Buy'
                if df['Ichimoku_A'].iloc[-1] < df['Ichimoku_B'].iloc[-1]:
                    return 'sell', 'ICHIMOKU A Sell'
                return None, 'ICHIMOKU Neutral'

            # Aroon up/down
            if indicator == 'Aroon_up' and 'Aroon_up' in df.columns and 'Aroon_down' in df.columns and 'Aroon_down' in selected_indicators:
                if df['Aroon_up'].iloc[-1] > df['Aroon_down'].iloc[-1]:
                    return 'buy', 'AROON Buy'
                if df['Aroon_up'].iloc[-1] < df['Aroon_down'].iloc[-1]:
                    return 'sell', 'AROON Sell'
                return None, 'AROON Neutral'

            # ADX directional context
            if indicator == 'ADX' and {'ADX','ADX_pos','ADX_neg'}.issubset(df.columns):
                if df['ADX'].iloc[-1] > 20:
                    if df['ADX_pos'].iloc[-1] > df['ADX_neg'].iloc[-1]:
                        return 'buy', 'ADX Buy'
                    else:
                        return 'sell', 'ADX Sell'
                return None, 'ADX Weak'

            # Simple zero-cross oscillators
            zero_up = {
                'TRIX': 'TRIX', 'TSI': 'TSI', 'AO': 'AO',
                'PVO_hist': 'PVO_hist', 'Awesome_Oscillator': 'Awesome_Oscillator',
                'Percentage_Price_Oscillator': 'Percentage_Price_Oscillator',
                'Percentage_Volume_Oscillator': 'Percentage_Volume_Oscillator', 'DPO': 'DPO'
            }
            if indicator in zero_up and zero_up[indicator] in df.columns:
                val = _safe_float(df[zero_up[indicator]].iloc[-1])
                if val > 0:
                    return 'buy', f"{indicator} > 0"
                if val < 0:
                    return 'sell', f"{indicator} < 0"
                return None, f"{indicator} ~ 0"

            # ROC with thresholds
            if indicator == 'ROC' and 'ROC' in df.columns:
                roc = _safe_float(df['ROC'].iloc[-1])
                buy_above = _safe_float(settings.get('buy_above', 2))
                sell_below = _safe_float(settings.get('sell_below', -2))
                if roc > buy_above:
                    return 'buy', f"ROC > {buy_above} ({roc:.2f})"
                if roc < sell_below:
                    return 'sell', f"ROC < {sell_below} ({roc:.2f})"
                return None, f"ROC Neutral ({roc:.2f})"

            # Stochastic and signal
            if indicator == 'Stochastic' and 'Stochastic' in df.columns:
                val = _safe_float(df['Stochastic'].iloc[-1])
                low = _safe_float(settings.get('buy_below', 15))
                high = _safe_float(settings.get('sell_above', 85))
                if val < low:
                    return 'buy', f"STOCH < {low}"
                if val > high:
                    return 'sell', f"STOCH > {high}"
                return None, f"STOCH Neutral"
            if indicator == 'Stochastic_signal' and 'Stochastic_signal' in df.columns:
                val = _safe_float(df['Stochastic_signal'].iloc[-1])
                low = _safe_float(settings.get('buy_below', 15))
                high = _safe_float(settings.get('sell_above', 85))
                if val < low:
                    return 'buy', f"STOCH SIG < {low}"
                if val > high:
                    return 'sell', f"STOCH SIG > {high}"
                return None, f"STOCH SIG Neutral"

            # Ultimate Oscillator
            if indicator == 'Ultimate_Oscillator' and 'Ultimate_Oscillator' in df.columns:
                val = _safe_float(df['Ultimate_Oscillator'].iloc[-1])
                if val < 30:
                    return 'buy', 'Ultimate_Oscillator < 30'
                if val > 70:
                    return 'sell', 'Ultimate_Oscillator > 70'
                return None, 'Ultimate_Oscillator Neutral'

            # Williams %R
            if indicator == 'WR' and 'WR' in df.columns:
                val = _safe_float(df['WR'].iloc[-1])
                if val < -80:
                    return 'buy', 'WR < -80'
                if val > -20:
                    return 'sell', 'WR > -20'
                return None, 'WR Neutral'

            # CCI
            if indicator == 'CCI' and 'CCI' in df.columns:
                val = _safe_float(df['CCI'].iloc[-1])
                if val < -150:
                    return 'buy', 'CCI < -150'
                if val > 150:
                    return 'sell', 'CCI > 150'
                return None, 'CCI Neutral'

            # Mass Index (range heuristic)
            if indicator == 'Mass_Index' and 'Mass_Index' in df.columns:
                val = _safe_float(df['Mass_Index'].iloc[-1])
                if val < 26.5:
                    return 'buy', 'Mass Index < 26.5'
                if val > 27:
                    return 'sell', 'Mass Index > 27'
                return None, 'Mass Index Neutral'

            # OBV trend over N bars
            if indicator == 'OBV' and 'OBV' in df.columns and len(df) > 5:
                lookback = int(settings.get('obv_lookback', 5))
                if len(df) > lookback:
                    obv_trend = _safe_float(df['OBV'].iloc[-1] - df['OBV'].iloc[-lookback])
                    if obv_trend > 0:
                        return 'buy', 'OBV rising'
                    if obv_trend < 0:
                        return 'sell', 'OBV falling'
                return None, 'OBV Neutral'

            # CMF
            if indicator == 'CMF' and 'CMF' in df.columns:
                cmf = _safe_float(df['CMF'].iloc[-1])
                thr = _safe_float(settings.get('abs_threshold', 0.1))
                if cmf > thr:
                    return 'buy', f'CMF > {thr}'
                if cmf < -thr:
                    return 'sell', f'CMF < {-thr}'
                return None, 'CMF Neutral'

            # FI/EOM sign
            if indicator == 'FI' and 'FI' in df.columns:
                val = _safe_float(df['FI'].iloc[-1])
                return ('buy' if val > 0 else 'sell' if val < 0 else None), 'FI sign'
            if indicator == 'EOM' and 'EOM' in df.columns:
                val = _safe_float(df['EOM'].iloc[-1])
                return ('buy' if val > 0 else 'sell' if val < 0 else None), 'EOM sign'

            # VPT/NVI trend 1-bar
            if indicator == 'VPT' and 'VPT' in df.columns and len(df) > 1:
                return ('buy' if df['VPT'].iloc[-1] > df['VPT'].iloc[-2] else 'sell' if df['VPT'].iloc[-1] < df['VPT'].iloc[-2] else None), 'VPT trend'
            if indicator == 'NVI' and 'NVI' in df.columns and len(df) > 1:
                return ('buy' if df['NVI'].iloc[-1] > df['NVI'].iloc[-2] else 'sell' if df['NVI'].iloc[-1] < df['NVI'].iloc[-2] else None), 'NVI trend'

            # VWAP distance
            if indicator == 'VWAP' and 'VWAP' in df.columns and price is not None:
                vwap = _safe_float(df['VWAP'].iloc[-1])
                price_safe = _safe_float(price)
                if vwap and vwap != 0:
                    dist = (price_safe - vwap) / vwap * 100.0
                    if not math.isfinite(dist):
                        return None, 'VWAP distance invalid'
                    up = _safe_float(settings.get('buy_threshold_pct_above', 0.2))
                    down = _safe_float(settings.get('sell_threshold_pct_below', -0.2))
                    if dist > up:
                        return 'buy', f'VWAP +{dist:.2f}%'
                    if dist < down:
                        return 'sell', f'VWAP {dist:.2f}%'
                return None, 'VWAP Neutral'

            # Professional volume logic: OBV/NVI/VPT vs EMA cross
            if indicator in ('OBV','NVI','VPT') and indicator in df.columns:
                method = settings.get('method', 'ema_cross')
                ma_window = int(settings.get('ma_window', 20))
                cross_rule = settings.get('cross_rule', 'bullish')
                series = df[indicator].astype(float)
                if method == 'ema_cross' and len(series) >= max(3, ma_window):
                    ema_val = series.ewm(span=ma_window, adjust=False).mean().iloc[-1]
                    cur = _safe_float(series.iloc[-1])
                    if cur > ema_val:
                        return ('buy' if cross_rule == 'bullish' else None), f'{indicator} > EMA{ma_window}'
                    if cur < ema_val:
                        return ('sell' if cross_rule == 'bullish' else None), f'{indicator} < EMA{ma_window}'
                    return None, f'{indicator} ~ EMA{ma_window}'
                # Fallback: slope check (uptrend/downtrend)
                if len(series) >= 3:
                    if series.iloc[-1] > series.iloc[-2] > series.iloc[-3]:
                        return 'buy', f'{indicator} uptrend'
                    if series.iloc[-1] < series.iloc[-2] < series.iloc[-3]:
                        return 'sell', f'{indicator} downtrend'
                return None, f'{indicator} Neutral'

            # Bollinger: price touch
            if indicator in ('BB_upper','BB_lower','BB_middle') and {'BB_upper','BB_lower'}.issubset(df.columns):
                buy_on_lower = bool(settings.get('buy_on_lower_touch', False))
                sell_on_upper = bool(settings.get('sell_on_upper_touch', False))
                if buy_on_lower and price is not None and price < df['BB_lower'].iloc[-1]:
                    return 'buy', 'BB lower touch'
                if sell_on_upper and price is not None and price > df['BB_upper'].iloc[-1]:
                    return 'sell', 'BB upper touch'
                return None, 'BB Neutral'

            # Bollinger percent/width
            if indicator == 'BB_percent' and 'BB_percent' in df.columns:
                val = _safe_float(df['BB_percent'].iloc[-1])
                low = _safe_float(settings.get('buy_below', 0))
                high = _safe_float(settings.get('sell_above', 1))
                if val < low:
                    return 'buy', f'BB% < {low} ({val:.2f})'
                if val > high:
                    return 'sell', f'BB% > {high} ({val:.2f})'
                return None, f'BB% Neutral ({val:.2f})'
            if indicator == 'BB_width' and 'BB_width' in df.columns:
                width = _safe_float(df['BB_width'].iloc[-1])
                # Protection Linux: calcul sécurisé de la moyenne
                try:
                    bb_series = df['BB_width'].dropna()
                    if len(bb_series) > 0:
                        ref = _safe_float(settings.get('reference', _safe_float(bb_series.mean())))
                    else:
                        ref = _safe_float(settings.get('reference', width))
                except Exception:
                    ref = _safe_float(settings.get('reference', width))
                direction = settings.get('direction', 'sell_if_above')
                if direction == 'sell_if_above' and width > ref:
                    return 'sell', f'BB width > mean ({width:.4f} > {ref:.4f})'
                if direction == 'buy_if_above' and width > ref:
                    return 'buy', f'BB width > mean ({width:.4f} > {ref:.4f})'
                if direction == 'sell_if_below' and width < ref:
                    return 'sell', f'BB width < mean ({width:.4f} < {ref:.4f})'
                if direction == 'buy_if_below' and width < ref:
                    return 'buy', f'BB width < mean ({width:.4f} < {ref:.4f})'
                return None, 'BB width Neutral'

            # Donchian channels breakout
            if indicator in ('Donchian_upper','Donchian_lower') and {'Donchian_upper','Donchian_lower'}.issubset(df.columns):
                if price is not None and price > df['Donchian_upper'].iloc[-1]:
                    return 'sell', 'Donchian upper breakout'
                if price is not None and price < df['Donchian_lower'].iloc[-1]:
                    return 'buy', 'Donchian lower breakout'
                return None, 'Donchian Neutral'

            # Donchian middle/percent/width
            if indicator == 'Donchian_middle' and 'Donchian_middle' in df.columns and price is not None:
                if price > df['Donchian_middle'].iloc[-1]:
                    return 'buy', 'Donchian middle: price above'
                if price < df['Donchian_middle'].iloc[-1]:
                    return 'sell', 'Donchian middle: price below'
                return None, 'Donchian middle Neutral'
            if indicator == 'Donchian_percent' and 'Donchian_percent' in df.columns:
                val = _safe_float(df['Donchian_percent'].iloc[-1])
                low = _safe_float(settings.get('buy_below', 0))
                high = _safe_float(settings.get('sell_above', 1))
                if val < low:
                    return 'buy', f'Donch% < {low} ({val:.2f})'
                if val > high:
                    return 'sell', f'Donch% > {high} ({val:.2f})'
                return None, 'Donch% Neutral'
            if indicator == 'Donchian_width' and 'Donchian_width' in df.columns:
                width = _safe_float(df['Donchian_width'].iloc[-1])
                # Protection Linux: calcul sécurisé de la moyenne
                try:
                    donch_series = df['Donchian_width'].dropna()
                    if len(donch_series) > 0:
                        ref = _safe_float(settings.get('reference', _safe_float(donch_series.mean())))
                    else:
                        ref = _safe_float(settings.get('reference', width))
                except Exception:
                    ref = _safe_float(settings.get('reference', width))
                direction = settings.get('direction', 'sell_if_above')
                if direction == 'sell_if_above' and width > ref:
                    return 'sell', 'Donch width > mean'
                if direction == 'buy_if_above' and width > ref:
                    return 'buy', 'Donch width > mean'
                if direction == 'sell_if_below' and width < ref:
                    return 'sell', 'Donch width < mean'
                if direction == 'buy_if_below' and width < ref:
                    return 'buy', 'Donch width < mean'
                return None, 'Donch width Neutral'

            # Keltner channels
            if indicator in ('Keltner_upper','Keltner_lower') and {'Keltner_upper','Keltner_lower'}.issubset(df.columns) and price is not None:
                if price < df['Keltner_lower'].iloc[-1]:
                    return 'buy', 'Keltner lower touch'
                if price > df['Keltner_upper'].iloc[-1]:
                    return 'sell', 'Keltner upper touch'
                return None, 'Keltner Neutral'
            if indicator == 'Keltner_middle' and 'Keltner_middle' in df.columns and price is not None:
                if price > df['Keltner_middle'].iloc[-1]:
                    return 'buy', 'Keltner middle: price above'
                if price < df['Keltner_middle'].iloc[-1]:
                    return 'sell', 'Keltner middle: price below'
                return None, 'Keltner middle Neutral'
            if indicator == 'Keltner_percent' and 'Keltner_percent' in df.columns:
                val = _safe_float(df['Keltner_percent'].iloc[-1])
                low = _safe_float(settings.get('buy_below', 0))
                high = _safe_float(settings.get('sell_above', 1))
                if val < low:
                    return 'buy', f'Kelt% < {low} ({val:.2f})'
                if val > high:
                    return 'sell', f'Kelt% > {high} ({val:.2f})'
                return None, 'Kelt% Neutral'
            if indicator == 'Keltner_width' and 'Keltner_width' in df.columns:
                width = _safe_float(df['Keltner_width'].iloc[-1])
                # Protection Linux: calcul sécurisé de la moyenne
                try:
                    kelt_series = df['Keltner_width'].dropna()
                    if len(kelt_series) > 0:
                        ref = _safe_float(settings.get('reference', _safe_float(kelt_series.mean())))
                    else:
                        ref = _safe_float(settings.get('reference', width))
                except Exception:
                    ref = _safe_float(settings.get('reference', width))
                direction = settings.get('direction', 'sell_if_above')
                if direction == 'sell_if_above' and width > ref:
                    return 'sell', 'Kelt width > mean'
                if direction == 'buy_if_above' and width > ref:
                    return 'buy', 'Kelt width > mean'
                if direction == 'sell_if_below' and width < ref:
                    return 'sell', 'Kelt width < mean'
                if direction == 'buy_if_below' and width < ref:
                    return 'buy', 'Kelt width < mean'
                return None, 'Kelt width Neutral'

            # ATR / Ulcer Index / True Range
            if indicator == 'ATR' and 'ATR' in df.columns:
                val = _safe_float(df['ATR'].iloc[-1])
                # Protection Linux: calcul sécurisé de la moyenne
                try:
                    atr_series = df['ATR'].dropna()
                    if len(atr_series) > 0:
                        ref = _safe_float(settings.get('reference', _safe_float(atr_series.mean())))
                    else:
                        ref = _safe_float(settings.get('reference', val))
                except Exception:
                    ref = _safe_float(settings.get('reference', val))
                direction = settings.get('direction', 'sell_if_above')
                if direction == 'sell_if_above' and val > ref:
                    return 'sell', 'ATR > mean'
                if direction == 'buy_if_above' and val > ref:
                    return 'buy', 'ATR > mean'
                if direction == 'sell_if_below' and val < ref:
                    return 'sell', 'ATR < mean'
                if direction == 'buy_if_below' and val < ref:
                    return 'buy', 'ATR < mean'
                return None, 'ATR Neutral'
            if indicator == 'Ulcer_Index' and 'Ulcer_Index' in df.columns:
                val = _safe_float(df['Ulcer_Index'].iloc[-1])
                # Protection Linux: calcul sécurisé de la moyenne
                try:
                    ulcer_series = df['Ulcer_Index'].dropna()
                    if len(ulcer_series) > 0:
                        ref = _safe_float(settings.get('reference', _safe_float(ulcer_series.mean())))
                    else:
                        ref = _safe_float(settings.get('reference', val))
                except Exception:
                    ref = _safe_float(settings.get('reference', val))
                direction = settings.get('direction', 'sell_if_above')
                if direction == 'sell_if_above' and val > ref:
                    return 'sell', 'Ulcer > mean'
                if direction == 'buy_if_above' and val > ref:
                    return 'buy', 'Ulcer > mean'
                if direction == 'sell_if_below' and val < ref:
                    return 'sell', 'Ulcer < mean'
                if direction == 'buy_if_below' and val < ref:
                    return 'buy', 'Ulcer < mean'
                return None, 'Ulcer Neutral'
            if indicator == 'True_Range' and 'True_Range' in df.columns:
                val = _safe_float(df['True_Range'].iloc[-1])
                # Protection Linux: calcul sécurisé de la moyenne
                try:
                    tr_series = df['True_Range'].dropna()
                    if len(tr_series) > 0:
                        ref = _safe_float(settings.get('reference', _safe_float(tr_series.mean())))
                    else:
                        ref = _safe_float(settings.get('reference', val))
                except Exception:
                    ref = _safe_float(settings.get('reference', val))
                direction = settings.get('direction', 'sell_if_above')
                if direction == 'sell_if_above' and val > ref:
                    return 'sell', 'TR > mean'
                if direction == 'buy_if_above' and val > ref:
                    return 'buy', 'TR > mean'
                if direction == 'sell_if_below' and val < ref:
                    return 'sell', 'TR < mean'
                if direction == 'buy_if_below' and val < ref:
                    return 'buy', 'TR < mean'
                return None, 'TR Neutral'

            # Returns
            if indicator == 'Daily_Return' and 'Daily_Return' in df.columns:
                val = _safe_float(df['Daily_Return'].iloc[-1])
                bias = _safe_float(settings.get('zero_bias', 0.0))
                if val > bias:
                    return 'buy', f'Daily_Return > {bias}'
                if val < bias:
                    return 'sell', f'Daily_Return < {bias}'
                return None, f'Daily_Return ~ {bias}'
            if indicator == 'Cumulative_Return' and 'Cumulative_Return' in df.columns:
                val = _safe_float(df['Cumulative_Return'].iloc[-1])
                if 'reference' in settings:
                    ref = _safe_float(settings['reference'])
                    ref_src = 'cfg'
                else:
                    mode = settings.get('reference_mode', 'mean')
                    # Protection Linux: calcul sécurisé de la moyenne
                    try:
                        cumret_series = df['Cumulative_Return'].dropna()
                        if len(cumret_series) > 0:
                            ref = _safe_float(cumret_series.mean())
                        else:
                            ref = _safe_float(val)
                        ref_src = 'mean'
                    except Exception:
                        ref = _safe_float(val)
                        ref_src = 'fallback'
                if val > ref:
                    return 'buy', f'CumRet > {ref_src}'
                if val < ref:
                    return 'sell', f'CumRet < {ref_src}'
                return None, 'CumRet Neutral'
            if indicator == 'Daily_Log_Return' and 'Daily_Log_Return' in df.columns:
                val = _safe_float(df['Daily_Log_Return'].iloc[-1])
                bias = _safe_float(settings.get('zero_bias', 0.0))
                if val > bias:
                    return 'buy', f'LogRet > {bias}'
                if val < bias:
                    return 'sell', f'LogRet < {bias}'
                return None, f'LogRet ~ {bias}'

            # ATR/Ulcer/others left neutral by default
            return None, f"No rule for {indicator}"
        except Exception as e:
            return None, f"{indicator} error: {e}"


    def evaluate_market(self, df, selected_indicators, threshold, symbol, timeframe):
        """
        Évalue le marché basé sur les indicateurs sélectionnés et renvoie l'action recommandée (achat, vente ou rien).
        """
        try:
            buy_signals = 0
            sell_signals = 0
            total_indicators = len(selected_indicators)
            indicators_examined = 0

            if df is None or df.empty:
                return None, "Error: Dataframe is empty or None"

            # Calculate all indicators first
            self.calculate_indicators(df)

            # Process each indicator
            for indicator in selected_indicators:
                # Unified decision logic path using _indicator_decision
                try:
                    decision, debug_msg = self._indicator_decision(df, indicator, selected_indicators)
                    if decision == 'buy':
                        buy_signals += 1
                    elif decision == 'sell':
                        sell_signals += 1
                    indicators_examined += 1
                    if debug_msg:
                        print(debug_msg)
                    # Skip legacy per-indicator logic below
                    continue
                except Exception as e:
                    self.display_to_status_bar("Error", f"Error processing indicator '{indicator}': {e}")
                    indicators_examined += 1
                    continue
                try:
                    """
                    if indicator == 'SMA':
                        if df['close'].iloc[-1] > df['SMA'].iloc[-1]:
                            buy_signals += 1
                            print('SMA Buy')

                    if indicator == 'SMA':
                        price = df['close'].iloc[-1]
                        sma = df['SMA'].iloc[-1]
                        price_distance = (price - sma) / sma * 100

                        # Thresholds from settings (defaults preserved)
                        sma_settings = {}
                        try:
                            if hasattr(self, 'indicator_settings') and isinstance(self.indicator_settings, dict):
                                sma_settings = self.indicator_settings.get('SMA', {}) or {}
                        except Exception:
                            sma_settings = {}
                        up_thr = float(sma_settings.get('buy_threshold_pct_above', 0.5))
                        down_thr = float(sma_settings.get('sell_threshold_pct_below', -0.5))

                        if price_distance > up_thr:
                            buy_signals += 1
                            print(f'SMA Buy - Distance: {price_distance:.2f}% (thr {up_thr})')
                        elif price_distance < down_thr:
                            sell_signals += 1
                            print(f'SMA Sell - Distance: {price_distance:.2f}% (thr {down_thr})')
                    """
                    if indicator == 'EMA':
                        price = df['close'].iloc[-1]
                        ema = df['EMA'].iloc[-1]
                        price_distance = (price - ema) / ema * 100

                        ema_settings = {}
                        try:
                            if hasattr(self, 'indicator_settings') and isinstance(self.indicator_settings, dict):
                                ema_settings = self.indicator_settings.get('EMA', {}) or {}
                        except Exception:
                            ema_settings = {}
                        up_thr = float(ema_settings.get('buy_threshold_pct_above', 0.3))
                        down_thr = float(ema_settings.get('sell_threshold_pct_below', -0.3))

                        if price_distance > up_thr:
                            buy_signals += 1
                            print(f'EMA Buy - Distance: {price_distance:.2f}% (thr {up_thr})')
                        elif price_distance < down_thr:
                            sell_signals += 1
                            print(f'EMA Sell - Distance: {price_distance:.2f}% (thr {down_thr})')

                    """
                    if indicator == 'WMA':
                        if df['close'].iloc[-1] > df['WMA'].iloc[-1]:
                            buy_signals += 1
                            print('WMA Buy')
                        elif df['close'].iloc[-1] < df['WMA'].iloc[-1]:
                            sell_signals += 1
                            print('WMA Sell')

                    if indicator == 'WMA':
                        if df['close'].iloc[-1] > df['WMA'].iloc[-1]:
                            buy_signals += 1
                            print('WMA Buy')
                        elif df['close'].iloc[-1] < df['WMA'].iloc[-1]:
                            sell_signals += 1
                            print('WMA Sell')
                    """
                    if indicator == 'WMA':
                        price = df['close'].iloc[-1]
                        wma = df['WMA'].iloc[-1]
                        price_distance = (price - wma) / wma * 100

                        wma_settings = {}
                        try:
                            if hasattr(self, 'indicator_settings') and isinstance(self.indicator_settings, dict):
                                wma_settings = self.indicator_settings.get('WMA', {}) or {}
                        except Exception:
                            wma_settings = {}
                        up_thr = float(wma_settings.get('buy_threshold_pct_above', 0.4))
                        down_thr = float(wma_settings.get('sell_threshold_pct_below', -0.4))

                        if price_distance > up_thr:
                            buy_signals += 1
                            print(f'WMA Buy - Distance: {price_distance:.2f}% (thr {up_thr})')
                        elif price_distance < down_thr:
                            sell_signals += 1
                            print(f'WMA Sell - Distance: {price_distance:.2f}% (thr {down_thr})')


                    if indicator == 'ADX':
                        if df['ADX'].iloc[-1] > 20:
                            if df['ADX_pos'].iloc[-1] > df['ADX_neg'].iloc[-1]:
                                buy_signals += 1
                                print('ADX Buy')
                            else:
                                sell_signals += 1
                                print('ADX Sell')

                    if indicator == 'Vortex_pos' and 'Vortex_neg' in selected_indicators:
                        if df['Vortex_pos'].iloc[-1] > df['Vortex_neg'].iloc[-1]:
                            buy_signals += 1
                            print('VORTEX Buy')
                        elif df['Vortex_pos'].iloc[-1] < df['Vortex_neg'].iloc[-1]:
                            sell_signals += 1
                            print('VORTEX Sell')

                    if indicator == 'TRIX':
                        if df['TRIX'].iloc[-1] > 0:
                            buy_signals += 1
                            print('TRIX Buy')
                        elif df['TRIX'].iloc[-1] < 0:
                            sell_signals += 1
                            print('TRIX Sell')

                    if indicator == 'Mass_Index':
                        if df['Mass_Index'].iloc[-1] > 27:
                            sell_signals += 1
                            print('MASS INDEX Buy')
                        elif df['Mass_Index'].iloc[-1] < 26.5:
                            buy_signals += 1
                            print('MASS INDEX Sell')

                    if indicator == 'CCI':
                        if df['CCI'].iloc[-1] > 150:
                            sell_signals += 1
                            print('CCI Buy')
                        elif df['CCI'].iloc[-1] < -150:
                            buy_signals += 1
                            print('CCI Sell')

                    if indicator == 'DPO':
                        if df['DPO'].iloc[-1] > 0:
                            buy_signals += 1
                            print('DPO Buy')
                        elif df['DPO'].iloc[-1] < 0:
                            sell_signals += 1
                            print('DPO Sell')

                    if indicator == 'KST' and 'KST_signal' in selected_indicators:
                        if df['KST'].iloc[-1] > df['KST_signal'].iloc[-1]:
                            buy_signals += 1
                            print('KST Buy')
                        elif df['KST'].iloc[-1] < df['KST_signal'].iloc[-1]:
                            sell_signals += 1
                            print('KST Sell')

                    if indicator == 'Ichimoku_A' and 'Ichimoku_B' in selected_indicators:
                        if df['Ichimoku_A'].iloc[-1] > df['Ichimoku_B'].iloc[-1]:
                            buy_signals += 1
                            print('ICHIMOKU A Buy')
                        elif df['Ichimoku_A'].iloc[-1] < df['Ichimoku_B'].iloc[-1]:
                            sell_signals += 1
                            print('ICHIMOKU A Sell')

                    if indicator == 'Aroon_up' and 'Aroon_down' in selected_indicators:
                        if df['Aroon_up'].iloc[-1] > df['Aroon_down'].iloc[-1]:
                            buy_signals += 1
                            print('AROON Buy')
                        elif df['Aroon_up'].iloc[-1] < df['Aroon_down'].iloc[-1]:
                            sell_signals += 1
                            print('AROON Sell')

                    if indicator == 'STC':
                        if df['STC'].iloc[-1] > 50:
                            buy_signals += 1
                            print('STC Buy')
                        elif df['STC'].iloc[-1] < 50:
                            sell_signals += 1
                            print('STC Sell')

                    if indicator == 'RSI':
                        if df['RSI'].iloc[-1] < 25:
                            buy_signals += 1
                            print('RSI Buy')
                        elif df['RSI'].iloc[-1] > 75:
                            sell_signals += 1
                            print('RSI Sell')

                    if indicator == 'Stochastic':
                        if df['Stochastic'].iloc[-1] < 15:
                            buy_signals += 1
                            print('STOCHASTIC Buy')
                        elif df['Stochastic'].iloc[-1] > 85:
                            sell_signals += 1
                            print('STOCHASTIC Sell')

                    if indicator == 'Stochastic_signal':
                        if df['Stochastic_signal'].iloc[-1] < 15:
                            buy_signals += 1
                            print('STOCHASTIC SIGNAL Buy')
                        elif df['Stochastic_signal'].iloc[-1] > 85:
                            sell_signals += 1
                            print('STOCHASTIC SIGNAL Buy')

                    if indicator == 'TSI':
                        if df['TSI'].iloc[-1] > 0:
                            buy_signals += 1
                            print('TSI Buy')
                        elif df['TSI'].iloc[-1] < 0:
                            sell_signals += 1
                            print('TSI Sell')

                    if indicator == 'Ultimate_Oscillator':
                        if df['Ultimate_Oscillator'].iloc[-1] < 30:
                            buy_signals += 1
                            print('Ultimate_Oscillator Buy')
                        elif df['Ultimate_Oscillator'].iloc[-1] > 70:
                            sell_signals += 1
                            print('Ultimate_Oscillator Sell')

                    if indicator == 'WR':
                        if df['WR'].iloc[-1] < -80:
                            buy_signals += 1
                            print('WR Buy')
                        elif df['WR'].iloc[-1] > -20:
                            sell_signals += 1
                            print('WR Sell')

                    if indicator == 'AO':
                        if df['AO'].iloc[-1] > 0:
                            buy_signals += 1
                            print('AO Buy')
                        elif df['AO'].iloc[-1] < 0:
                            sell_signals += 1
                            print('AO Sell')

                    if indicator == 'ROC':
                        if df['ROC'].iloc[-1] > 2:
                            buy_signals += 1
                            print('ROC Buy')
                        elif df['ROC'].iloc[-1] < -2:
                            sell_signals += 1
                            print('ROC Sell')

                    if indicator == 'PVO' and 'PVO_signal' in selected_indicators:
                        if df['PVO'].iloc[-1] > df['PVO_signal'].iloc[-1]:
                            buy_signals += 1
                            print('PVO Buy')
                        elif df['PVO'].iloc[-1] < df['PVO_signal'].iloc[-1]:
                            sell_signals += 1
                            print('PVO Sell')

                    if indicator == 'PVO_hist':
                        if df['PVO_hist'].iloc[-1] > 0:
                            buy_signals += 1
                            print('PVO_hist Buy')
                        elif df['PVO_hist'].iloc[-1] < 0:
                            sell_signals += 1
                            print('PVO_hist Sell')

                    if indicator == 'Awesome_Oscillator':
                        if df['Awesome_Oscillator'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Awesome_Oscillator Buy')
                        elif df['Awesome_Oscillator'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Awesome_Oscillator Sell')

                    if indicator == 'Percentage_Price_Oscillator':
                        if df['Percentage_Price_Oscillator'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Percentage_Price_Oscillator Buy')
                        elif df['Percentage_Price_Oscillator'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Percentage_Price_Oscillator Sell')

                    if indicator == 'Percentage_Volume_Oscillator':
                        if df['Percentage_Volume_Oscillator'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Percentage_Volume_Oscillator Buy')
                        elif df['Percentage_Volume_Oscillator'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Percentage_Volume_Oscillator Sell')

                    # Volume Indicators
                    """
                    if indicator == 'OBV':
                        if df['OBV'].iloc[-1] > 0:
                            buy_signals += 1
                            print('OBV Buy')
                        elif df['OBV'].iloc[-1] < 0:
                            sell_signals += 1
                            print('OBV Sell')
                    if indicator == 'OBV':
                        if df['OBV'].iloc[-1] > df['OBV'].iloc[-2]:
                            buy_signals += 1
                            print('OBV Buy')
                        elif df['OBV'].iloc[-1] < df['OBV'].iloc[-2]:
                            sell_signals += 1
                            print('OBV Sell')
                    """
                    if indicator == 'OBV' and len(df) > 5:
                        obv_current = df['OBV'].iloc[-1]
                        obv_prev = df['OBV'].iloc[-5]
                        obv_trend = obv_current - obv_prev

                        if obv_trend > 0:
                            buy_signals += 1
                            print('OBV Buy')
                        elif obv_trend < 0:
                            sell_signals += 1
                            print('OBV Sell')



                    """
                    if indicator == 'CMF':
                        if df['CMF'].iloc[-1] > 0:
                            buy_signals += 1
                            print('CMF Buy')
                        elif df['CMF'].iloc[-1] < 0:
                            sell_signals += 1
                            print('CMF Sell')
                    """
                    if indicator == 'CMF':
                        cmf = df['CMF'].iloc[-1]

                        if cmf > 0.1:  # Flux d'argent positif fort
                            buy_signals += 1
                            print(f'CMF Buy - Value: {cmf:.3f}')
                        elif cmf < -0.1:  # Flux d'argent négatif fort
                            sell_signals += 1
                            print(f'CMF Sell - Value: {cmf:.3f}')

                    if indicator == 'FI':
                        if df['FI'].iloc[-1] > 0:
                            buy_signals += 1
                            print('FI Buy')
                        elif df['FI'].iloc[-1] < 0:
                            sell_signals += 1
                            print('FI Sell')

                    if indicator == 'EOM':
                        if df['EOM'].iloc[-1] > 0:
                            buy_signals += 1
                            print('EOM Buy')
                        elif df['EOM'].iloc[-1] < 0:
                            sell_signals += 1
                            print('EOM Sell')

                    if indicator == 'VPT':
                        if df['VPT'].iloc[-1] > df['VPT'].iloc[-2]:
                            buy_signals += 1
                            print('VPT Buy')
                        elif df['VPT'].iloc[-1] < df['VPT'].iloc[-2]:
                            sell_signals += 1
                            print('VPT Sell')

                    if indicator == 'NVI':
                        if df['NVI'].iloc[-1] > df['NVI'].iloc[-2]:
                            buy_signals += 1
                            print('NVI Buy')
                        elif df['NVI'].iloc[-1] < df['NVI'].iloc[-2]:
                            sell_signals += 1
                            print('NVI Sell')
                    """
                    if indicator == 'VWAP':
                        if df['close'].iloc[-1] > df['VWAP'].iloc[-1]:
                            buy_signals += 1
                            print('VWAP Buy')
                        elif df['close'].iloc[-1] < df['VWAP'].iloc[-1]:
                            sell_signals += 1
                            print('VWAP Sell')
                    """
                    if indicator == 'VWAP':
                        price = df['close'].iloc[-1]
                        vwap = df['VWAP'].iloc[-1]
                        price_distance = (price - vwap) / vwap * 100

                        if price_distance > 0.2:
                            buy_signals += 1
                            print(f'VWAP Buy - Distance: {price_distance:.2f}%')
                        elif price_distance < -0.2:
                            sell_signals += 1
                            print(f'VWAP Sell - Distance: {price_distance:.2f}%')

                    # Volatility Indicators
                    if indicator == 'BB_upper' and 'BB_lower' in selected_indicators:
                        if df['close'].iloc[-1] < df['BB_lower'].iloc[-1]:
                            buy_signals += 1
                            print('BB upper Buy')
                        elif df['close'].iloc[-1] > df['BB_upper'].iloc[-1]:
                            sell_signals += 1
                            print('BB upper Sell')

                    if indicator == 'BB_middle':
                        if df['close'].iloc[-1] > df['BB_middle'].iloc[-1]:
                            buy_signals += 1
                            print('BB middle Buy')
                        elif df['close'].iloc[-1] < df['BB_middle'].iloc[-1]:
                            sell_signals += 1
                            print('BB middle Sell')

                    if indicator == 'BB_percent':
                        if df['BB_percent'].iloc[-1] < 0:
                            buy_signals += 1
                            print('BB percent Buy')
                        elif df['BB_percent'].iloc[-1] > 1:
                            sell_signals += 1
                            print('BB percent Sell')

                    if indicator == 'BB_width':
                        if df['BB_width'].iloc[-1] > df['BB_width'].mean():
                            sell_signals += 1
                            print('BB width Buy')
                        elif df['BB_width'].iloc[-1] < df['BB_width'].mean():
                            buy_signals += 1
                            print('BB width Sell')

                    if indicator == 'ATR':
                        if df['ATR'].iloc[-1] > df['ATR'].mean():
                            sell_signals += 1
                            print('ATR Buy')
                        elif df['ATR'].iloc[-1] < df['ATR'].mean():
                            buy_signals += 1
                            print('ATR Sell')

                    if indicator == 'Keltner_upper' and 'Keltner_lower' in selected_indicators:
                        if df['close'].iloc[-1] < df['Keltner_lower'].iloc[-1]:
                            buy_signals += 1
                            print('Keltner upper Buy')
                        elif df['close'].iloc[-1] > df['Keltner_upper'].iloc[-1]:
                            sell_signals += 1
                            print('Keltner upper Sell')

                    if indicator == 'Keltner_middle':
                        if df['close'].iloc[-1] > df['Keltner_middle'].iloc[-1]:
                            buy_signals += 1
                            print('Keltner middle Buy')
                        elif df['close'].iloc[-1] < df['Keltner_middle'].iloc[-1]:
                            sell_signals += 1
                            print('Keltner middle Sell')

                    if indicator == 'Keltner_percent':
                        if df['Keltner_percent'].iloc[-1] < 0:
                            buy_signals += 1
                            print('Keltner percent Buy')
                        elif df['Keltner_percent'].iloc[-1] > 1:
                            sell_signals += 1
                            print('Keltner percent Sell')

                    if indicator == 'Keltner_width':
                        if df['Keltner_width'].iloc[-1] > df['Keltner_width'].mean():
                            sell_signals += 1
                            print('Keltner_width Buy')
                        elif df['Keltner_width'].iloc[-1] < df['Keltner_width'].mean():
                            buy_signals += 1
                            print('Keltner_width Sell')

                    if indicator == 'Donchian_upper' and 'Donchian_lower' in selected_indicators:
                        if df['close'].iloc[-1] < df['Donchian_lower'].iloc[-1]:
                            buy_signals += 1
                            print('Donchian_upper Buy')
                        elif df['close'].iloc[-1] > df['Donchian_upper'].iloc[-1]:
                            sell_signals += 1
                            print('Donchian_upper Sell')

                    if indicator == 'Donchian_middle':
                        if df['close'].iloc[-1] > df['Donchian_middle'].iloc[-1]:
                            buy_signals += 1
                            print('Donchian_middle Buy')
                        elif df['close'].iloc[-1] < df['Donchian_middle'].iloc[-1]:
                            sell_signals += 1
                            print('Donchian_middle Sell')

                    if indicator == 'Donchian_percent':
                        if df['Donchian_percent'].iloc[-1] < 0:
                            buy_signals += 1
                            print('Donchian_percent Buy')
                        elif df['Donchian_percent'].iloc[-1] > 1:
                            sell_signals += 1
                            print('Donchian_percent Sell')

                    if indicator == 'Donchian_width':
                        if df['Donchian_width'].iloc[-1] > df['Donchian_width'].mean():
                            sell_signals += 1
                            print('Donchian_width Buy')
                        elif df['Donchian_width'].iloc[-1] < df['Donchian_width'].mean():
                            buy_signals += 1
                            print('Donchian_width Sell')

                    if indicator == 'Ulcer_Index':
                        if df['Ulcer_Index'].iloc[-1] > df['Ulcer_Index'].mean():
                            sell_signals += 1
                            print('Ulcer_Index Buy')
                        elif df['Ulcer_Index'].iloc[-1] < df['Ulcer_Index'].mean():
                            buy_signals += 1
                            print('Ulcer_Index Sell')

                    if indicator == 'True_Range':
                        if df['True_Range'].iloc[-1] > df['True_Range'].mean():
                            sell_signals += 1
                            print('True Range Buy')
                        elif df['True_Range'].iloc[-1] < df['True_Range'].mean():
                            buy_signals += 1
                            print('True Range Sell')

                    if indicator == 'Daily_Return':
                        if df['Daily_Return'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Daily_Return Buy')
                        elif df['Daily_Return'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Daily_Return Sell')

                    if indicator == 'Cumulative_Return':
                        if df['Cumulative_Return'].iloc[-1] > df['Cumulative_Return'].mean():
                            buy_signals += 1
                            print('Cumulative_Return Buy')
                        elif df['Cumulative_Return'].iloc[-1] < df['Cumulative_Return'].mean():
                            sell_signals += 1
                            print('Cumulative_Return Sell')

                    if indicator == 'Daily_Log_Return':
                        if df['Daily_Log_Return'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Daily_Log_Return Buy')
                        elif df['Daily_Log_Return'].iloc[-1] < 0:
                            sell_signals += 1

                    indicators_examined += 1
                except Exception as e:
                    self.display_to_status_bar("Error", f"Error processing indicator '{indicator}': {e}")
                    continue

            score = buy_signals - sell_signals

            if buy_signals > threshold * indicators_examined:
                colored_score = color_for_message('buy', score)
                formatted_message = f"Buy Signal {symbol} Triggered in {timeframe}"
                self.display_to_nano_terminal(formatted_message, colored_score, "info")
                return 'buy', score
            elif sell_signals > threshold * indicators_examined:
                colored_score = color_for_message('sell', score)
                formatted_message = f"Sell Signal {symbol} Triggered in {timeframe}"
                self.display_to_nano_terminal(formatted_message, colored_score, "info")
                return 'sell', score
            else:
                colored_score = color_for_message('No Action', score)
                formatted_message = f"No Action Signal {symbol} Triggered in {timeframe}"
                self.display_to_nano_terminal(formatted_message, colored_score, "info")
                return "no_action", score


        except Exception as e:
            self.display_to_status_bar("Error", f"Error processing indicators: {e}")
            return None, 0



    def calculate_weighted_score(self, combined_scores):
        if not combined_scores:
            return None

        weight_dict = {
            '1s': 0.005,
            '1m': 0.01,
            '3m': 0.02,
            '5m': 0.03,
            '15m': 0.04,
            '30m': 0.05,
            '1h': 0.1,
            '2h': 0.15,
            '4h': 0.2,
            '6h': 0.3,
            '8h': 0.4,
            '12h': 0.5,
            '1d': 0.6,
            '3d': 0.7,
            '1w': 0.8,
            '1M': 1.0,
            '3M': 1.0
        }

        weighted_score = sum(score * weight_dict[tf] for tf, score in combined_scores)
        return weighted_score

    def update_button_states(self):
        if self.start_button and self.stop_button:
            if self.nano_trading_active:
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
            else:
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)


    def check_and_start_trading(self):
        if self.nano_trading_active:
            self.display_to_status_bar("Error", "Nano trading bot is already running.")
            return

        # Désactiver le bouton recovery après avoir cliqué sur Start Trading
        if hasattr(self, 'main_window') and hasattr(self.main_window, 'config_recovery_button'):
            self.main_window.config_recovery_button.setEnabled(False)

        selected_exchanges = [exchange for exchange, var in self.nano_trading_exchange_vars.items() if var.isChecked()]
        if not selected_exchanges:
            self.display_to_status_bar("Error", "No exchanges selected for trading.")
            return

        self.nano_trading_selected_exchanges = selected_exchanges

        # Mettre à jour immédiatement la status bar avec le nombre d'échanges sélectionnés
        self.update_enhanced_status_bar("🔄 Preparing", "Exchange Selection", {}, len(selected_exchanges), 0, 0.0)

        # Vérifier que les conditions d'achat et de vente sont remplies avant de démarrer
        if not self.check_conditions_filled():
            #self.show_error_message("You must fill at least one buy condition and one sell condition before starting the bot.")
            return

        # Gérer la configuration de récupération intelligemment en arrière-plan
        self.display_to_status_bar("Checking", "Checking recovery configuration...")
        # Déplacer en arrière-plan pour éviter le blocage Linux lors de l'accès aux widgets Qt
        try:
            QTimer.singleShot(100, self._handle_recovery_configuration_async)
        except Exception as e:
            print(f"Error starting animation: {e}")
            QTimer.singleShot(1000, self._handle_recovery_configuration_async)

        # Déplacer la vérification des trades après l'initialisation pour éviter le blocage Linux
        # Cette vérification sera faite dans _start_trading_phase2 après la création des threads
        print("🔍 DEBUG: Skipping trades check at startup to prevent Linux blocking - will check after thread initialization")

        # Mettre à jour les balances des échanges
        self.update_exchanges_balances()

        # Mettre à jour le widget ConnectedExchangesBalancesWidget avec les échanges sélectionnés
        self.connected_exchanges_widget.initialize_active_exchanges()
        # Forcer l'initialisation de l'affichage PNL au démarrage du bot
        try:
            self.connected_exchanges_widget.initialize_profits()
        except Exception as e:
            print(f"Error initializing PNL at bot start: {e}")

        # Démarrer le bot
        self.start_nano_trading()






    def check_and_restart_trading(self):
         # Mettre à jour les balances des échanges
        self.update_exchanges_balances()

        # Démarrer le bot
        self.start_nano_trading()



    def start_nano_trading(self):
        # Clean up any existing threads and workers with proper synchronization
        if self.trading_thread:
            self.trading_thread.quit()
            if not self.trading_thread.wait(5000):  # Wait max 5 seconds
                print("⚠️ Warning: Trading thread did not stop gracefully")
                self.trading_thread.terminate()
                self.trading_thread.wait(2000)
            self.trading_thread = None

        if self.trading_worker:
            self.trading_worker = None

        # Add small delay to ensure cleanup is complete (Linux stability)
        try:
            QTimer.singleShot(100, self._start_trading_phase2)
        except Exception as e:
            print(f"Error starting trading: {e}")
            try:
                QTimer.singleShot(1000, self._start_trading_phase2)
            except Exception as e:
                print(f"Error starting trading: {e}")
                QTimer.singleShot(2000, self._start_trading_phase2)

    def _start_trading_phase2(self):
        """Phase 2: Create and start trading components with staggered initialization"""
        try:
            # Create new worker and thread
            self.trading_worker = TradingWorker(self, self.nano_processor, self.dashboard, self.db)
            self.trading_thread = QThread()

            # Move worker to thread
            self.trading_worker.moveToThread(self.trading_thread)

            # Connect signals with queued connections to ensure thread safety
            self.trading_worker.update_terminal.connect(self.display_to_nano_terminal, Qt.QueuedConnection)
            self.trading_worker.update_status.connect(self.display_to_status_bar, Qt.QueuedConnection)
            self.trading_worker.update_enhanced_status.connect(self.update_enhanced_status_bar, Qt.QueuedConnection)
            self.trading_worker.add_trade_signal.connect(self.dashboard.exchanges_trades_tables.add_trade_to_table, Qt.QueuedConnection)
            self.trading_worker.update_exchanges_balances_signal.connect(self.update_exchanges_balances, Qt.QueuedConnection)
            self.trading_worker.update_statistics_signal.connect(
                self.dashboard.exchange_symbol_trade_data_widget.update_widget_content, Qt.QueuedConnection
            )
            self.trading_worker.trading_finished.connect(self.on_trading_finished, Qt.QueuedConnection)

            # Start the thread
            self.trading_thread.start()

            # Wait for thread to be fully started before proceeding
            if not self.trading_thread.isRunning():
                print("❌ Error: Trading thread failed to start")
                return

            # Invoke start_trading in the worker's thread
            QMetaObject.invokeMethod(self.trading_worker, 'start_trading', Qt.QueuedConnection)

            # Initialiser la barre de statut avec le bon nombre d'échanges sélectionnés
            selected_exchanges_count = len(self.nano_trading_selected_exchanges)
            self.update_enhanced_status_bar("🚀 Starting", "Bot Initialization", {}, selected_exchanges_count, 0, 0.0)

            # Update bot state and buttons
            self.nano_trading_active = True
            self.update_button_states()

            # Start the bot timer early in phase 2
            if hasattr(self, 'main_window') and self.main_window:
                self.main_window.start_bot_timer()
                # Mettre à jour l'état du bouton de récupération de configuration
                self.main_window.update_config_recovery_button_state()

            # Maintenant que les threads sont initialisés, vérifier les trades existants (Linux safe)
            print("🔍 DEBUG: Checking for existing trades after thread initialization...")
            if hasattr(self.dashboard, 'exchanges_trades_tables'):
                try:
                    active_trades = self.db.get_all_trades()
                    print(f"🔍 DEBUG: Active trades from DB: {len(active_trades) if active_trades else 0}")
                    
                    if not active_trades:  # Seulement si aucun trade actif
                        print("🔍 DEBUG: No active trades - showing starting message")
                        self.dashboard.exchanges_trades_tables.show_starting_message()
                    else:
                        print(f"ℹ️ {len(active_trades)} active trades found - loading them into UI")
                        # Charger les trades existants dans l'interface
                        self.dashboard.exchanges_trades_tables.load_existing_trades_from_database(self.db)
                except Exception as e:
                    print(f"⚠️ Warning: Could not check existing trades: {e}")
                    # En cas d'erreur, afficher le message de démarrage par défaut
                    self.dashboard.exchanges_trades_tables.show_starting_message()

            self.display_to_nano_terminal("NANO|System", "Nano trading bot has been started.", "info")

            # Synchronize unique_traded_pairs with existing trades (use existing logic from recovery)
            try:
                existing_trades = self.db.get_all_trades()
                self.unique_traded_pairs = set()
                for trade in existing_trades:
                    symbol = trade.get('symbol', '')
                    if symbol:
                        self.unique_traded_pairs.add(symbol)
                print(f"🔄 Synchronized {len(self.unique_traded_pairs)} existing trades: {self.unique_traded_pairs}")
            except Exception as e:
                print(f"⚠️ Warning: Could not sync existing trades: {e}")
                self.unique_traded_pairs = set()

            # Connect critical signals early to ensure UI updates work immediately
            self._connect_processor_signals()

            # Envoyer l'email de démarrage immédiatement après l'initialisation du bot
            #self._send_startup_notifications()
            
            # Start the PriceWatcher with increased delay to avoid resource conflicts on Linux
            try:
                QTimer.singleShot(2000, self._start_price_watcher)  # 2 second delay for Linux stability
                print("✅ PriceWatcher scheduled to start in 2 seconds")
            except Exception as e:
                print(f"❌ Error scheduling PriceWatcher: {e}")
                # Fallback: démarrer directement si QTimer échoue
                QTimer.singleShot(4000, self._start_price_watcher)

        except Exception as e:
            print(f"❌ Error in trading startup phase 2: {e}")
            self.display_to_status_bar("Error", f"Failed to start trading: {e}")

    def _connect_processor_signals(self):
        """Connect PriceWatcher signals early to ensure immediate UI updates"""
        try:
            # Connect signals from the PriceWatcherWorker (early connection for immediate UI updates)
            if hasattr(self.nano_processor, 'worker') and self.nano_processor.worker:
                self.nano_processor.worker.trade_updated_signal.connect(
                    self.dashboard.exchanges_trades_tables.update_trade_in_ui, Qt.QueuedConnection
                )
                self.nano_processor.worker.trade_profit_signal.connect(
                    self.dashboard.connected_exchanges_widget.update_profits, Qt.QueuedConnection
                )
                self.nano_processor.worker.update_enhanced_status.connect(self.update_enhanced_status_bar, Qt.QueuedConnection)
                self.db.trade_removed_from_db_signal.connect(
                    self.dashboard.exchanges_trades_tables.remove_trade_from_table, Qt.QueuedConnection
                )
                self.nano_processor.worker.graph_removed_signal.connect(
                    self.dashboard.exchange_symbol_trade_data_widget.remove_symbol_from_graph, Qt.QueuedConnection
                )
                self.nano_processor.worker.add_trade_signal.connect(
                    self.dashboard.exchanges_trades_tables.add_trade_to_table, Qt.QueuedConnection
                )
                print("✅ PriceWatcher signals connected early")
        except Exception as e:
            print(f"⚠️ Warning: Could not connect processor signals early: {e}")


    def _start_price_watcher(self):
        """Phase 3: Start PriceWatcher after trading thread is stable"""
        try:
            self.nano_processor.start_watching()
            print("✅ PriceWatcher started successfully")
        except Exception as e:
            print(f"❌ Error starting PriceWatcher: {e}")
            self.display_to_status_bar("Error", f"Failed to start price monitoring: {e}")







    def fetch_and_update_balances(self, exchange_name):
        exchange_class = getattr(ccxt, exchange_name)
        exchange = exchange_class({
            'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
            'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
            'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
        })

        try:
            balance = exchange.fetch_balance()
            # Update the balance_initial_by_currency dictionary
            self.balance_initial_by_currency['USDT'] += balance['free'].get('USDT', 0)
            self.balance_initial_by_currency['USDC'] += balance['free'].get('USDC', 0)
            self.balance_initial_by_currency['BTC'] += balance['free'].get('BTC', 0)

            # If using a custom symbol
            custom_symbol = self.get_selected_currency()
            if custom_symbol and custom_symbol not in ['USDT', 'USDC', 'BTC']:
                self.balance_initial_by_currency[custom_symbol] += balance['free'].get(custom_symbol, 0)

            self.display_to_nano_terminal("NANO|A-3", f"Balance for {exchange_name}: {balance['free']}", "info")
            # Passez les échanges sélectionnés directement à la méthode




        except Exception as e:
            self.display_to_status_bar("Error", f"Error fetching balance for {exchange_name}: {e}")


    def update_exchanges_balances(self):
        balances = []
        selected_currency = self.get_selected_currency()
        
        # Initialize balance_initial_by_currency ONLY if empty (preserve initial values)
        if not hasattr(self, 'balance_initial_by_currency') or not self.balance_initial_by_currency:
            self.balance_initial_by_currency = {}
            
        tokens_to_check = ['USDT', 'USDC', 'BTC']
        if selected_currency and selected_currency not in tokens_to_check:
            tokens_to_check.append(selected_currency)
            
        # Only initialize tokens that don't exist yet (preserve initial values)
        for token in tokens_to_check:
            if token not in self.balance_initial_by_currency:
                self.balance_initial_by_currency[token] = 0
        for exchange_name in self.nano_trading_selected_exchanges:
            exchange_class = getattr(ccxt, exchange_name)
            exchange = exchange_class({
                'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
            })

            try:
                balance = exchange.fetch_balance()
                for token in tokens_to_check:
                    if token in balance['free']:
                        balances.append({
                            'exchange': exchange_name,
                            'token': token,
                            'balance': balance['free'][token]
                        })
                        # Only update initial balance if it was 0 (first time)
                        if self.balance_initial_by_currency[token] == 0:
                            self.balance_initial_by_currency[token] += balance['free'][token]
            except Exception as e:
                self.display_to_nano_terminal("NANO|A-3", f"Error fetching balance for {exchange_name}: {e}", "error")

        # Update the balances in the connected widget
        self.connected_exchanges_widget.update_balances(balances)



    def get_active_trades_count(self):
        """
        Retourne le nombre de trades actifs (ceux qui ne sont pas encore terminés).
        """
        # Récupérer les trades depuis la base de données
        active_trades = self.db.get_all_trades()

        return len(active_trades)



    def calculate_average_buy_price(self, symbol, existing_trade_id, last_close, amount_to_trade):
        try:
            # Récupérer les détails du trade existant (s'il existe)
            existing_trade = self.db.get_trade_by_id(existing_trade_id)

            # Si un trade existe déjà, on met à jour le prix moyen d'achat
            if existing_trade:
                # Quantité totale déjà achetée
                existing_amount_traded = existing_trade['amount_to_trade']

                # Prix moyen d'achat actuel
                existing_average_price = existing_trade['buy_price']

                # Calculer le nouveau montant total
                total_amount_traded = existing_amount_traded + amount_to_trade

                # Calculer la valeur totale actuelle du trade
                total_value_traded = (existing_amount_traded * existing_average_price) + (amount_to_trade * last_close)

                # Calculer et retourner le nouveau prix moyen d'achat
                new_average_price = total_value_traded / total_amount_traded
                return new_average_price
            else:
                # Si aucun trade existant, on retourne simplement le prix actuel
                return last_close
        except Exception as e:
            print(f"Error calculating average buy price for {symbol}: {e}")
            return last_close

    def load_recovery_configuration_detailed(self):
        """
        Charge la configuration de récupération depuis config_recovery.nta
        et applique tous les paramètres à l'interface utilisateur.
        (Version détaillée avec logs)
        """
        try:
            config_file = "config_recovery.nta"

            if not os.path.exists(config_file):
                print(f"Recovery configuration file not found: {config_file}")
                return False

            with open(config_file, 'r') as f:
                config_data = json.load(f)

            print(f"Loading recovery configuration from {config_file}...")

            # 1. Charger les exchanges
            if 'exchanges' in config_data:
                exchanges = config_data['exchanges']
                print(f"Recovery exchanges: {exchanges}")

                # Décocher tous les exchanges d'abord
                for exchange_name in self.nano_trading_exchange_vars:
                    self.nano_trading_exchange_vars[exchange_name].setChecked(False)

                # Cocher les exchanges de récupération
                for exchange in exchanges:
                    if exchange in self.nano_trading_exchange_vars:
                        self.nano_trading_exchange_vars[exchange].setChecked(True)
                        print(f"✅ Exchange activated: {exchange}")

            # 2. Charger le montant de trade
            if 'trade_amount' in config_data:
                trade_amount = config_data['trade_amount']
                if hasattr(self, 'trade_amount_entry'):
                    self.trade_amount_entry.setText(str(trade_amount))
                    print(f"✅ Trade amount set: {trade_amount}")

            # 3. Charger le nombre de trades
            if 'num_trades' in config_data:
                num_trades = config_data['num_trades']
                if hasattr(self, 'num_trades_entry'):
                    self.num_trades_entry.setText(str(num_trades))
                    print(f"✅ Number of trades set: {num_trades}")

            # 4. Charger la timeframe
            if 'timeframe' in config_data:
                timeframe = config_data['timeframe']
                if hasattr(self, 'timeframe_combobox'):
                    index = self.timeframe_combobox.findText(timeframe)
                    if index >= 0:
                        self.timeframe_combobox.setCurrentIndex(index)
                        print(f"✅ Timeframe set: {timeframe}")

            # 5. Charger le threshold
            if 'threshold' in config_data:
                threshold = config_data['threshold']
                if hasattr(self, 'threshold_entry'):
                    self.threshold_entry.setText(str(threshold))
                    print(f"✅ Threshold set: {threshold}")

            # 6. Charger les symboles
            if 'symbols' in config_data:
                symbols = config_data['symbols']

                # Charger USDT
                if 'USDT' in symbols and hasattr(self, 'usdt_checkbox'):
                    self.usdt_checkbox.setChecked(symbols['USDT'])
                    print(f"✅ Symbol USDT: {'enabled' if symbols['USDT'] else 'disabled'}")

                # Charger USDC
                if 'USDC' in symbols and hasattr(self, 'usdc_checkbox'):
                    self.usdc_checkbox.setChecked(symbols['USDC'])
                    print(f"✅ Symbol USDC: {'enabled' if symbols['USDC'] else 'disabled'}")

                # Charger BTC
                if 'BTC' in symbols and hasattr(self, 'btc_checkbox'):
                    self.btc_checkbox.setChecked(symbols['BTC'])
                    print(f"✅ Symbol BTC: {'enabled' if symbols['BTC'] else 'disabled'}")

                # Charger custom symbol
                if 'custom_symbol' in symbols and hasattr(self, 'custom_symbol_entry'):
                    self.custom_symbol_entry.setText(symbols['custom_symbol'])
                    print(f"✅ Custom symbol: {symbols['custom_symbol']}")

                # Déclencher la sélection de devise après chargement des symboles
                try:
                    self.on_currency_selected()
                    print(f"✅ Currency selection triggered after recovery")
                except Exception as e:
                    print(f"⚠️ Warning: Could not trigger currency selection: {str(e)}")

            # 7. Charger les indicateurs
            if 'indicators' in config_data:
                indicators = config_data['indicators']
                for indicator, enabled in indicators.items():
                    if hasattr(self, f'{indicator.lower()}_var'):
                        getattr(self, f'{indicator.lower()}_var').setChecked(enabled)
                        print(f"✅ Indicator {indicator}: {'enabled' if enabled else 'disabled'}")

            # 8. Charger les timeframes depth
            if 'timeframes_depth' in config_data:
                timeframes_depth = config_data['timeframes_depth']
                for tf, enabled in timeframes_depth.items():
                    if hasattr(self, f'timeframe_{tf}_var'):
                        getattr(self, f'timeframe_{tf}_var').setChecked(enabled)
                        print(f"✅ Timeframe depth {tf}: {'enabled' if enabled else 'disabled'}")

            # 9. Charger les ordres d'achat (buy_setup)
            if 'buy_setup' in config_data and config_data['buy_setup']:
                self.load_buy_setup_from_recovery(config_data['buy_setup'])

            # 10. Charger les ordres de vente (sell_setup)
            if 'sell_setup' in config_data and config_data['sell_setup']:
                self.load_sell_setup_from_recovery(config_data['sell_setup'])

            print("✅ Recovery configuration loaded successfully!")

            # Mettre à jour les balances des exchanges après chargement de la configuration
            try:
                self.update_exchanges_balances()
                print("✅ Exchange balances updated after recovery")
            except Exception as e:
                print(f"⚠️ Warning: Could not update exchange balances: {str(e)}")

            return True

        except Exception as e:
            print(f"❌ Error loading recovery configuration: {str(e)}")
            return False

    def load_buy_setup_from_recovery(self, buy_setup):
        """
        Charge les ordres d'achat depuis la configuration de récupération.
        """
        try:
            # Nettoyer les buy_rows existants de manière sécurisée
            self.clear_buy_rows_safely()

            # Recréer les buy_rows depuis la configuration
            for buy_order in buy_setup:
                percentage = buy_order.get('percentage', 1.5)  # Valeur numérique par défaut
                amount = buy_order.get('amount', 10)  # Valeur numérique par défaut

                try:
                    # Ajouter une nouvelle ligne d'achat avec les valeurs de récupération
                    self.add_buy_row(self.buy_setup_layout)

                    # Configurer la dernière ligne ajoutée
                    if self.buy_rows:
                        last_row = self.buy_rows[-1]

                        # Définir le pourcentage avec vérification
                        if 'percentage_combobox' in last_row and last_row['percentage_combobox'] is not None:
                            try:
                                combobox = last_row['percentage_combobox']
                                # Formater le pourcentage pour correspondre aux valeurs de la combobox
                                if percentage == int(percentage):
                                    # Si c'est un nombre entier, ne pas afficher .0
                                    percentage_text = f"{int(percentage)}%"
                                else:
                                    # Si c'est un nombre décimal, garder les décimales
                                    percentage_text = f"{percentage}%"

                                if combobox.isEditable():
                                    combobox.setCurrentText(percentage_text)
                                else:
                                    index = combobox.findText(percentage_text)
                                    if index >= 0:
                                        combobox.setCurrentIndex(index)
                                    else:
                                        print(f"⚠️ Percentage {percentage_text} not found in combobox options")
                            except RuntimeError:
                                print(f"⚠️ Combobox widget deleted for percentage {percentage}")

                        # Définir le montant avec vérification
                        if 'amount_entry' in last_row and last_row['amount_entry'] is not None:
                            try:
                                last_row['amount_entry'].setText(str(amount))
                            except RuntimeError:
                                print(f"⚠️ Amount entry widget deleted for amount {amount}")

                    print(f"✅ Buy order loaded: {percentage}% - ${amount}")

                except Exception as e:
                    print(f"⚠️ Error loading buy order {percentage}%: {str(e)}")
                    continue

            print(f"✅ {len(buy_setup)} buy orders processed from recovery")

        except Exception as e:
            print(f"❌ Error loading buy setup: {str(e)}")

    def clear_buy_rows_safely(self):
        """Nettoie les buy_rows de manière sécurisée."""
        try:
            # Supprimer les widgets de manière sécurisée
            for row in self.buy_rows:
                try:
                    if 'percentage_combobox' in row and row['percentage_combobox'] is not None:
                        row['percentage_combobox'].deleteLater()
                    if 'amount_entry' in row and row['amount_entry'] is not None:
                        row['amount_entry'].deleteLater()
                    if 'remove_button' in row and row['remove_button'] is not None:
                        row['remove_button'].deleteLater()
                except RuntimeError:
                    # Widget déjà supprimé
                    pass

            # Vider la liste
            self.buy_rows.clear()

        except Exception as e:
            print(f"Error clearing buy rows: {str(e)}")
            self.buy_rows.clear()  # Au minimum, vider la liste

    def load_sell_setup_from_recovery(self, sell_setup):
        """
        Charge les ordres de vente depuis la configuration de récupération.
        """
        try:
            # Nettoyer les sell_rows existants de manière sécurisée
            self.clear_sell_rows_safely()

            # Recréer les sell_rows depuis la configuration
            for sell_order in sell_setup:
                percentage = sell_order.get('percentage', 5.0)  # Valeur numérique par défaut
                sell_percentage = sell_order.get('sell_percentage', 100)  # Valeur numérique par défaut

                try:
                    # Ajouter une nouvelle ligne de vente avec les valeurs de récupération
                    self.add_sell_row(self.sell_setup_layout)

                    # Configurer la dernière ligne ajoutée
                    if self.sell_rows:
                        last_row = self.sell_rows[-1]

                        # Définir le pourcentage de profit avec vérification
                        if 'percentage_combobox' in last_row and last_row['percentage_combobox'] is not None:
                            try:
                                combobox = last_row['percentage_combobox']
                                # Formater le pourcentage pour correspondre aux valeurs de la combobox
                                if percentage == int(percentage):
                                    # Si c'est un nombre entier, ne pas afficher .0
                                    percentage_text = f"{int(percentage)}%"
                                else:
                                    # Si c'est un nombre décimal, garder les décimales
                                    percentage_text = f"{percentage}%"

                                if combobox.isEditable():
                                    combobox.setCurrentText(percentage_text)
                                else:
                                    index = combobox.findText(percentage_text)
                                    if index >= 0:
                                        combobox.setCurrentIndex(index)
                                    else:
                                        print(f"⚠️ Percentage {percentage_text} not found in combobox options")
                            except RuntimeError:
                                print(f"⚠️ Combobox widget deleted for percentage {percentage}")

                        # Définir le pourcentage de vente avec vérification
                        if 'sell_percentage_entry' in last_row and last_row['sell_percentage_entry'] is not None:
                            try:
                                last_row['sell_percentage_entry'].setText(str(sell_percentage))
                            except RuntimeError:
                                print(f"⚠️ Sell percentage entry widget deleted for {sell_percentage}%")

                    print(f"✅ Sell order loaded: +{percentage}% - {sell_percentage}%")

                except Exception as e:
                    print(f"⚠️ Error loading sell order +{percentage}%: {str(e)}")
                    continue

            print(f"✅ {len(sell_setup)} sell orders processed from recovery")

        except Exception as e:
            print(f"❌ Error loading sell setup: {str(e)}")

    def clear_sell_rows_safely(self):
        """Nettoie les sell_rows de manière sécurisée."""
        try:
            # Supprimer les widgets de manière sécurisée
            for row in self.sell_rows:
                try:
                    if 'percentage_combobox' in row and row['percentage_combobox'] is not None:
                        row['percentage_combobox'].deleteLater()
                    if 'sell_percentage_entry' in row and row['sell_percentage_entry'] is not None:
                        row['sell_percentage_entry'].deleteLater()
                    if 'remove_button' in row and row['remove_button'] is not None:
                        row['remove_button'].deleteLater()
                except RuntimeError:
                    # Widget déjà supprimé
                    pass

            # Vider la liste
            self.sell_rows.clear()

        except Exception as e:
            print(f"Error clearing sell rows: {str(e)}")
            self.sell_rows.clear()  # Au minimum, vider la liste



    def calculate_price_to_buy(self, row, average_buy_price):
        try:
            # Vérifier que la row et percentage_combobox existent
            if not row or "percentage_combobox" not in row:
                print("Error: Row or percentage_combobox not found")
                return average_buy_price

            # S'assurer que percentage_combobox est un QComboBox et qu'il n'est pas supprimé
            if isinstance(row["percentage_combobox"], QComboBox):
                try:
                    # Vérifier que l'objet Qt existe encore
                    buy_percentage = float(row["percentage_combobox"].currentText().replace("%", "")) / 100
                except RuntimeError as e:
                    print(f"QComboBox already deleted: {e}")
                    return average_buy_price
            else:
                # Si c'est déjà un str, utilisez directement la valeur
                buy_percentage = float(row["percentage_combobox"].replace("%", "")) / 100

            # Calculer le prix d'achat si différent du précédent
            buy_price = average_buy_price * (1 - buy_percentage)
            if buy_price != self.previous_buy_price:
                self.previous_buy_price = buy_price
                print(f"Calculated buy price: {buy_price} for percentage: {buy_percentage}")
            return buy_price

        except Exception as e:
            print(f"Error calculating price to buy: {e}")
            return average_buy_price

    def calculate_price_to_sell(self, row, average_buy_price, last_buy_price=None):
        """
        Calcule le prix de vente selon le mode configuré - utilisé lors de la configuration des trades
        """
        try:
            # S'assurer que percentage_combobox est un QComboBox et obtenir son texte
            if isinstance(row["percentage_combobox"], QComboBox):
                sell_percentage = float(row["percentage_combobox"].currentText().replace("%", "")) / 100
            else:
                # Si c'est déjà un str, utilisez directement la valeur
                sell_percentage = float(row["percentage_combobox"].replace("%", "")) / 100

            # Obtenir le mode de profit configuré
            profit_mode = self.get_profit_mode()

            if profit_mode == "fixed_profit":
                # Mode profit fixe pour l'affichage de configuration
                fixed_profit = self.get_fixed_profit_amount()
                sell_amount_pct = float(row.get('sell_percentage_entry', {}).text() or 100) / 100.0 if hasattr(row.get('sell_percentage_entry', {}), 'text') else 1.0
                Q_sell = max(sell_amount_pct, 1e-12)

                # Approximation pour l'affichage (sans frais pour simplifier)
                sell_price = average_buy_price + (fixed_profit / Q_sell)
                mode_info = f"Fixed Profit: {fixed_profit} USDT"

            elif profit_mode == "percentage_last" and last_buy_price:
                # Mode pourcentage basé sur le dernier prix d'achat
                sell_price = last_buy_price * (1 + sell_percentage)
                mode_info = f"Last Buy Price: {last_buy_price:.6f}"

            else:  # percentage_avg (défaut)
                # Mode pourcentage basé sur le prix moyen d'achat
                sell_price = average_buy_price * (1 + sell_percentage)
                mode_info = f"Average Buy Price: {average_buy_price:.6f}"

            # Log seulement si le prix change
            if not hasattr(self, 'previous_sell_price') or sell_price != self.previous_sell_price:
                self.previous_sell_price = sell_price
                print(f"📊 Config Preview - Sell price: {sell_price:.6f} ({mode_info}, +{sell_percentage*100:.1f}%)")

            return sell_price

        except Exception as e:
            print(f"❌ Error calculating price to sell in config: {e}")
            return average_buy_price


##############################################################
# Shutdown Program

    def shutdown_application(self):
        reply = QMessageBox.question(
            None,
            'Shutdown Confirmation',
            '🔄 <b>Shutdown Application</b><br><br>'
            '📊 This will close the application while <b>preserving your trades</b><br>'
            '💾 Your database and configuration will be kept for recovery<br>'
            '🚀 You can restart and continue trading later<br><br>'
            '<b>Are you sure you want to shutdown?</b>',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Arrêter le trading SANS supprimer la base de données
            self.stop_nano_trading()

            # Arrêter le PriceWatcher
            if self.nano_processor:
                try:
                    if self.nano_processor.worker:

                        # Déconnecter les signaux du worker
                        try:
                            self.nano_processor.worker.update_terminal.disconnect(self.display_to_nano_terminal)
                            # Déconnecter les autres signaux si nécessaire
                        except RuntimeError:
                            pass
                    send_shutdown_notifications()
                    self.nano_processor.stop_watching()
                    self.nano_processor = None
                except Exception as e:
                    print(f"Error stopping nano_processor: {e}")

            # S'assurer que tous les événements sont traités
            QCoreApplication.processEvents()

            # Fermer l'application
            QApplication.quit()
        else:
            pass

    def on_profit_mode_changed(self):
        """Gérer l'affichage/masquage des champs selon le mode de profit sélectionné"""
        mode = self.get_profit_mode()
        print(f"Profit calculation mode changed to: {mode}")

        # Gérer l'affichage du champ Fixed Profit Amount
        if mode == "fixed_profit":
            # Mode Fixed Profit : afficher le champ fixed profit, cacher les sell rows
            self.fixed_profit_label.setVisible(True)
            self.fixed_profit_entry.setVisible(True)
            self.currency_label.setVisible(True)
            # Mettre à jour le label de devise
            self.update_currency_label()
            # Gérer les sections
            self._set_buy_sections_visible(True)   # Buy toujours visible
            self._set_sell_sections_visible(False) # Sell caché (calculé automatiquement)
            print("🎯 Mode Fixed Profit activé - Section Sell cachée (calculée automatiquement)")
        else:
            # Modes Average Buy Price ou Last Buy Price : cacher le champ fixed profit, afficher les sell rows
            self.fixed_profit_label.setVisible(False)
            self.fixed_profit_entry.setVisible(False)
            self.currency_label.setVisible(False)
            # Gérer les sections
            self._set_buy_sections_visible(True)   # Buy visible
            self._set_sell_sections_visible(True)  # Sell visible
            print("📊 Mode Percentage activé - Toutes les sections visibles")

    def update_currency_label(self):
        """
        Met à jour le libellé de la devise en fonction de la sélection de l'utilisateur.
        """
        selected_currency = self.get_selected_currency()
        if selected_currency:
            self.currency_label.setText(selected_currency)
            # S'assurer que le label est visible si le mode Fixed Profit est sélectionné
            if hasattr(self, 'profit_mode_fixed') and self.profit_mode_fixed.isChecked():
                self.currency_label.setVisible(True)

    def on_fixed_profit_changed(self):
        """
        Appelée quand le montant de profit fixe change.
        """
        try:
            amount = float(self.fixed_profit_entry.text())
            print(f"Fixed profit amount changed to: {amount} {self.get_selected_currency()}")
        except ValueError:
            # Valeur invalide, ne rien faire
            pass

    def _set_buy_sections_visible(self, visible):
        """Afficher/masquer seulement les sections Buy"""
        try:
            # Afficher/masquer tous les widgets dans buy_rows
            for row in self.buy_rows:
                if 'percentage_combobox' in row and row['percentage_combobox']:
                    row['percentage_combobox'].setVisible(visible)
                if 'amount_entry' in row and row['amount_entry']:
                    row['amount_entry'].setVisible(visible)

            # Afficher/masquer la frame buy si elle existe
            if hasattr(self, 'buy_setup_frame'):
                self.buy_setup_frame.setVisible(visible)

        except Exception as e:
            print(f"Erreur lors de l'affichage/masquage des sections Buy: {e}")

    def _set_sell_sections_visible(self, visible):
        """Afficher/masquer seulement les sections Sell"""
        try:
            # Afficher/masquer tous les widgets dans sell_rows
            for row in self.sell_rows:
                try:
                    if 'percentage_combobox' in row and row['percentage_combobox']:
                        row['percentage_combobox'].setVisible(visible)
                    if 'sell_percentage_entry' in row and row['sell_percentage_entry']:
                        row['sell_percentage_entry'].setVisible(visible)
                    # Gérer les boutons (peuvent ne pas exister dans les anciennes lignes)
                    if 'add_button' in row and row['add_button']:
                        row['add_button'].setVisible(visible)
                    if 'remove_button' in row and row['remove_button']:
                        row['remove_button'].setVisible(visible)
                except Exception as row_error:
                    print(f"Erreur sur une ligne sell: {row_error}")
                    continue

            # Afficher/masquer la frame sell si elle existe
            if hasattr(self, 'sell_setup_frame'):
                self.sell_setup_frame.setVisible(visible)

            # Message informatif
            if visible:
                print("👁️ Sections Sell affichées")
            else:
                print("🙈 Sections Sell cachées (mode Fixed Profit)")

        except Exception as e:
            print(f"Erreur lors de l'affichage/masquage des sections Sell: {e}")

    def get_profit_mode(self):
        """Retourner le mode de profit actuel"""
        if self.profit_mode_fixed.isChecked():
            return "fixed_profit"
        elif self.profit_mode_last.isChecked():
            return "percentage_last"
        else:
            return "percentage_avg"  # Mode par défaut

    # Stop Loss Methods
    def get_stop_loss_percentage(self):
        """Retourne le pourcentage de stop loss saisi."""
        try:
            return float(self.stop_loss_input.text())  # Retourne la valeur telle quelle (peut être négative)
        except (ValueError, AttributeError):
            return 0.0  # Valeur par défaut si l'entrée n'est pas valide

    def set_stop_loss_percentage(self, percentage):
        """Définit le pourcentage de stop loss."""
        try:
            value = float(percentage)  # Accepte les valeurs négatives
            self.stop_loss_input.setText(str(value))
        except (ValueError, TypeError):
            self.stop_loss_input.setText("0.0")

    def is_stop_loss_enabled(self):
        """Retourne True si le stop loss est activé."""
        return hasattr(self, 'stop_loss_enabled_radio') and self.stop_loss_enabled_radio.isChecked()

    def get_stop_loss_mode(self):
        """Retourne le mode stop loss: 'with_buy_rows' ou 'without_buy_rows'."""
        if not self.is_stop_loss_enabled():
            return None

        if hasattr(self, 'stop_loss_with_buy_rows_radio') and self.stop_loss_with_buy_rows_radio.isChecked():
            return 'with_buy_rows'
        elif hasattr(self, 'stop_loss_without_buy_rows_radio') and self.stop_loss_without_buy_rows_radio.isChecked():
            return 'without_buy_rows'
        return 'with_buy_rows'  # Par défaut

    def set_stop_loss_enabled(self, enabled):
        """Active ou désactive le stop loss."""
        if hasattr(self, 'stop_loss_enabled_radio'):
            self.stop_loss_enabled_radio.setChecked(enabled)
            self.on_stop_loss_enabled_changed()

    def set_stop_loss_mode(self, mode):
        """Définit le mode stop loss."""
        if mode == 'with_buy_rows' and hasattr(self, 'stop_loss_with_buy_rows_radio'):
            self.stop_loss_with_buy_rows_radio.setChecked(True)
        elif mode == 'without_buy_rows' and hasattr(self, 'stop_loss_without_buy_rows_radio'):
            self.stop_loss_without_buy_rows_radio.setChecked(True)

        self.on_stop_loss_mode_changed()

    def on_stop_loss_changed(self):
        """Appelée quand la valeur du stop loss change - Validation en temps réel."""
        percentage = self.get_stop_loss_percentage()
        print(f"Stop loss percentage changed to: {percentage}%")

        # Validation intelligente en temps réel
        if (hasattr(self, 'stop_loss_enabled_radio') and
            self.stop_loss_enabled_radio.isChecked() and
            hasattr(self, 'stop_loss_with_buy_rows_radio') and
            self.stop_loss_with_buy_rows_radio.isChecked()):
            self.validate_stop_loss_percentage_realtime()

    def on_stop_loss_enabled_changed(self):
        """Appelée quand le stop loss est activé/désactivé."""
        is_enabled = self.stop_loss_enabled_radio.isChecked()
        self.stop_loss_mode_frame.setVisible(is_enabled)

        if is_enabled:
            print("Stop Loss enabled")
            # Appliquer la logique selon le mode sélectionné
            self.on_stop_loss_mode_changed()
        else:
            print("Stop Loss disabled")
            # Réafficher les buy rows si elles étaient cachées
            self._set_buy_sections_visible(True)

    def on_stop_loss_mode_changed(self):
        """Appelée quand le mode stop loss change (avec/sans buy rows)."""
        if not self.stop_loss_enabled_radio.isChecked():
            return

        if self.stop_loss_with_buy_rows_radio.isChecked():
            print("Stop Loss mode: WITH buy rows")
            self._set_buy_sections_visible(True)
            # Valider que le % stop loss < % dernier buy row
            self.validate_stop_loss_percentage()
        else:
            print("Stop Loss mode: WITHOUT buy rows - will sell total amount")
            self._set_buy_sections_visible(False)

    def validate_stop_loss_percentage(self):
        """Valide que le % stop loss est inférieur au % du dernier buy row."""
        if not hasattr(self, 'buy_rows') or not self.buy_rows:
            print("ℹ️ No buy rows configured - validation skipped")
            return True

        try:
            stop_loss_pct = self.get_stop_loss_percentage()

            # Si le stop loss est à 0, la validation est automatiquement réussie
            if stop_loss_pct == 0:
                print("ℹ️ Stop loss is 0% - no validation needed")
                return True

            # Obtenir tous les pourcentages des buy rows
            buy_percentages = []
            for row in self.buy_rows:
                try:
                    if 'percentage_combobox' in row and row['percentage_combobox'] is not None:
                        if hasattr(row['percentage_combobox'], 'currentText'):
                            pct_text = row['percentage_combobox'].currentText().replace('%', '')
                            buy_percentages.append(float(pct_text))
                        else:
                            # Si c'est déjà une string
                            pct_text = str(row['percentage_combobox']).replace('%', '')
                            buy_percentages.append(float(pct_text))
                except (ValueError, AttributeError, RuntimeError) as e:
                    print(f"Warning: Could not parse buy row percentage: {e}")
                    continue

            if buy_percentages:
                max_buy_pct = max(buy_percentages)
                print(f"🔍 Validation - Stop Loss: {stop_loss_pct}%, Buy Rows: {buy_percentages}, Max Buy %: {max_buy_pct}")

                # Pour un stop loss valide, il doit être supérieur au pourcentage maximum des buy rows
                # Exemple: Si buy rows = [1.5%, 3%, 4.5%], le stop loss doit être > 4.5%
                if stop_loss_pct <= max_buy_pct:
                    print(f"❌ INVALID: Stop loss {stop_loss_pct}% must be > {max_buy_pct}% (highest buy row)")
                    self.update_stop_loss_input_style(False)
                    self.show_stop_loss_error_message(stop_loss_pct, max_buy_pct)
                    return False
                else:
                    print(f"✅ VALID: Stop loss {stop_loss_pct}% > {max_buy_pct}% (highest buy row)")
                    self.update_stop_loss_input_style(True)
                    return True

        except Exception as e:
            print(f"❌ Error validating stop loss: {e}")
            return False

        # Si aucun buy row valide n'a été trouvé
        if not buy_percentages:
            print("ℹ️ No buy rows configured yet - stop loss validation skipped")
            return True

    def validate_stop_loss_percentage_realtime(self):
        """Validation en temps réel du stop loss (sans message d'erreur)."""
        try:
            stop_loss_pct = self.get_stop_loss_percentage()

            # Si le stop loss est à 0, la validation est automatiquement réussie
            if stop_loss_pct == 0:
                print("ℹ️ Stop loss is 0% - no validation needed")
                self.update_stop_loss_input_style(True)
                return True

            # Obtenir tous les pourcentages des buy rows
            buy_percentages = []
            for row in self.buy_rows:
                try:
                    if 'percentage_combobox' in row and row['percentage_combobox'] is not None:
                        if hasattr(row['percentage_combobox'], 'currentText'):
                            pct_text = row['percentage_combobox'].currentText().replace('%', '')
                            buy_percentages.append(float(pct_text))
                        else:
                            # Si c'est déjà une string
                            pct_text = str(row['percentage_combobox']).replace('%', '')
                            buy_percentages.append(float(pct_text))
                except (ValueError, AttributeError, RuntimeError) as e:
                    continue

            if buy_percentages:
                max_buy_pct = max(buy_percentages)
                print(f"🔍 Realtime Validation - Stop Loss: {stop_loss_pct}%, Buy Rows: {buy_percentages}, Max Buy %: {max_buy_pct}")

                # Pour un stop loss valide, il doit être supérieur au pourcentage maximum des buy rows
                if stop_loss_pct <= max_buy_pct:
                    # Style d'erreur mais pas de message popup en temps réel
                    print(f"❌ INVALID: Stop loss {stop_loss_pct}% must be > {max_buy_pct}% (highest buy row)")
                    self.update_stop_loss_input_style(False)
                    return False
                else:
                    print(f"✅ VALID: Stop loss {stop_loss_pct}% > {max_buy_pct}% (highest buy row)")
                    self.update_stop_loss_input_style(True)
                    return True
            else:
                # Pas de buy rows, donc valide
                self.update_stop_loss_input_style(True)
                return True

        except Exception as e:
            print(f"Error in realtime validation: {e}")
            return False

    def update_stop_loss_input_style(self, is_valid):
        """Met à jour le style de l'input stop loss selon la validité."""
        if not hasattr(self, 'stop_loss_input'):
            return

        try:
            if is_valid:
                # Style normal
                self.stop_loss_input.setStyleSheet(get_text_area_style(True))
            else:
                # Style d'erreur (bordure rouge)
                error_style = get_text_area_style(True) + """
                    QLineEdit {
                        border: 2px solid #ff4444 !important;
                        background-color: rgba(255, 68, 68, 0.1) !important;
                    }
                """
                self.stop_loss_input.setStyleSheet(error_style)
        except Exception as e:
            print(f"Error updating stop loss input style: {e}")

    def show_stop_loss_error_message(self, percentage, max_buy_pct):
        """Affiche un message d'erreur pour la validation du stop loss."""
        try:
            from PySide6.QtWidgets import QMessageBox
            
            if percentage <= 0:
                error_msg = "Stop Loss percentage must be greater than 0%"
            else:
                error_msg = f"Stop Loss {percentage}% must be > highest Buy Row {max_buy_pct}%"
            
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setWindowTitle("Stop Loss Validation Error")
            msg_box.setText(error_msg)
            msg_box.setInformativeText("Please adjust your Stop Loss percentage to be higher than your highest Buy Row percentage.")
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec()
            
        except Exception as e:
            print(f"Error showing stop loss error message: {e}")

    def on_buy_row_percentage_changed(self):
        """Appelée quand un pourcentage de buy row change - Revalide le stop loss."""
        print("🔄 Buy row percentage changed - revalidating stop loss...")
        
        # Si le stop loss est activé et en mode "with buy rows", revalider
        if (hasattr(self, 'stop_loss_enabled_radio') and
            self.stop_loss_enabled_radio.isChecked() and
            hasattr(self, 'stop_loss_with_buy_rows_radio') and
            self.stop_loss_with_buy_rows_radio.isChecked()):
            
            # Revalider le stop loss avec le nouveau pourcentage de buy row
            self.validate_stop_loss_percentage_realtime()

    def remove_buy_row_and_revalidate_stop_loss(self, frame):
        """Supprime une buy row et revalide le stop loss."""
        try:
            # Supprimer la buy row de la liste
            for i, row in enumerate(self.buy_rows):
                if 'frame' in row and row['frame'] == frame:
                    del self.buy_rows[i]
                    break
            
            # Supprimer le widget de l'interface
            frame.deleteLater()
            
            # Revalider le stop loss
            if (hasattr(self, 'stop_loss_enabled_radio') and
                self.stop_loss_enabled_radio.isChecked() and
                hasattr(self, 'stop_loss_with_buy_rows_radio') and
                self.stop_loss_with_buy_rows_radio.isChecked()):
                
                self.validate_stop_loss_percentage_realtime()
                
        except Exception as e:
            print(f"Error removing buy row and revalidating stop loss: {e}")

    def get_fixed_profit_amount(self):
        """Retourner le montant de profit fixe en USDT"""
        try:
            return float(self.fixed_profit_entry.text())
        except (ValueError, AttributeError):
            return 0.15  # Valeur par défaut

    def get_complete_sell_enabled(self):
        """Retourner si la vente complète des restes est activée"""
        # Pour l'instant, toujours activé par défaut
        # Peut être configuré plus tard via une checkbox dans l'interface
        return True

    def get_complete_sell_threshold_usdt(self):
        """Retourner le seuil minimum en USDT pour déclencher une vente complète"""
        return 0.1  # Seuil par défaut : 0.1 USDT

    def calculate_fixed_profit_sell_data(self, buy_price, amount_to_trade, fixed_profit_usdt):
        """
        Calcule le prix de vente et le pourcentage pour le mode Fixed Profit

        Args:
            buy_price (float): Prix d'achat moyen
            amount_to_trade (float): Montant à trader
            fixed_profit_usdt (float): Profit fixe désiré en USDT

        Returns:
            dict: {'sell_price': float, 'percentage': float}
        """
        try:
            if amount_to_trade <= 0:
                raise ValueError("Amount must be greater than 0")

            # Calcul du prix de vente nécessaire
            # Profit = (sell_price - buy_price) * amount_to_trade
            # sell_price = buy_price + (profit / amount_to_trade)
            sell_price = buy_price + (fixed_profit_usdt / amount_to_trade)

            # Calcul du pourcentage équivalent
            percentage = ((sell_price - buy_price) / buy_price) * 100

            return {
                'sell_price': sell_price,
                'percentage': percentage
            }

        except Exception as e:
            print(f"Error calculating fixed profit: {e}")
            # Retourner des valeurs par défaut
            return {
                'sell_price': buy_price * 1.03,  # +3% par défaut
                'percentage': 3.0
            }


##############################################################