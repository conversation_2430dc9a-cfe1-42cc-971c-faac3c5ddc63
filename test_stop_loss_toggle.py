#!/usr/bin/env python3
"""
Script de test pour vérifier le comportement du toggle Stop Loss
"""

import sys
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QCheckBox, QRadioButton, QFrame, QLabel, QPushButton
from PySide6.QtCore import Qt

class StopLossTestWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Stop Loss Toggle")
        self.setGeometry(100, 100, 400, 300)
        
        layout = QVBoxLayout()
        
        # Test avec QCheckBox (nouvelle solution)
        layout.addWidget(QLabel("Nouvelle solution avec QCheckBox:"))
        self.checkbox_stop_loss = QCheckBox("Enable Stop Loss (CheckBox)")
        self.checkbox_stop_loss.setChecked(False)
        self.checkbox_stop_loss.toggled.connect(self.on_checkbox_toggled)
        layout.addWidget(self.checkbox_stop_loss)
        
        # Frame pour les options (avec/sans buy rows)
        self.options_frame = QFrame()
        self.options_frame.setVisible(False)
        options_layout = QVBoxLayout(self.options_frame)
        
        self.with_buy_rows = QRadioButton("With Buy Rows")
        self.without_buy_rows = QRadioButton("Without Buy Rows")
        self.with_buy_rows.setChecked(True)
        
        options_layout.addWidget(self.with_buy_rows)
        options_layout.addWidget(self.without_buy_rows)
        
        layout.addWidget(self.options_frame)
        
        # Séparateur
        layout.addWidget(QLabel("=" * 50))
        
        # Test avec QRadioButton (ancienne solution - pour comparaison)
        layout.addWidget(QLabel("Ancienne solution avec QRadioButton:"))
        self.radio_stop_loss = QRadioButton("Enable Stop Loss (RadioButton)")
        self.radio_stop_loss.setChecked(False)
        self.radio_stop_loss.toggled.connect(self.on_radio_toggled)
        layout.addWidget(self.radio_stop_loss)
        
        # Frame pour les options radio
        self.radio_options_frame = QFrame()
        self.radio_options_frame.setVisible(False)
        radio_options_layout = QVBoxLayout(self.radio_options_frame)
        
        self.radio_with_buy_rows = QRadioButton("With Buy Rows (Radio)")
        self.radio_without_buy_rows = QRadioButton("Without Buy Rows (Radio)")
        self.radio_with_buy_rows.setChecked(True)
        
        radio_options_layout.addWidget(self.radio_with_buy_rows)
        radio_options_layout.addWidget(self.radio_without_buy_rows)
        
        layout.addWidget(self.radio_options_frame)
        
        # Bouton pour reset
        reset_btn = QPushButton("Reset All")
        reset_btn.clicked.connect(self.reset_all)
        layout.addWidget(reset_btn)
        
        # Status
        self.status_label = QLabel("Status: Prêt pour test")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
    
    def on_checkbox_toggled(self, checked):
        """Callback pour le QCheckBox"""
        self.options_frame.setVisible(checked)
        status = "CheckBox: Activé" if checked else "CheckBox: Désactivé"
        self.status_label.setText(status)
        print(f"✅ {status} - Options visibles: {checked}")
    
    def on_radio_toggled(self, checked):
        """Callback pour le QRadioButton"""
        self.radio_options_frame.setVisible(checked)
        status = "RadioButton: Activé" if checked else "RadioButton: Désactivé"
        self.status_label.setText(status)
        print(f"🔘 {status} - Options visibles: {checked}")
    
    def reset_all(self):
        """Reset tous les contrôles"""
        self.checkbox_stop_loss.setChecked(False)
        self.radio_stop_loss.setChecked(False)
        self.status_label.setText("Status: Reset effectué")
        print("🔄 Reset effectué")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    widget = StopLossTestWidget()
    widget.show()
    
    print("🚀 Test du comportement Stop Loss:")
    print("1. Testez le QCheckBox - il devrait pouvoir être coché/décoché")
    print("2. Testez le QRadioButton - il ne peut pas être décoché une fois coché")
    print("3. Les sections 'with/without buy rows' doivent apparaître/disparaître")
    
    sys.exit(app.exec())
