#nano_configuration



from PySide6.QtWidgets import (
    QVBoxLayout, QGridLayout, QWidget, QLabel, QCheckBox, QComboBox, QLineEdit, QPushButton,
    QFrame, QScrollArea, QHBoxLayout, QScrollBar, QMessageBox, QStatusBar, QSizePolicy, QSpacerItem, QFileDialog, QApplication, QDialog, QRadioButton, QButtonGroup
)

from PySide6.QtGui import QIcon
from PySide6 import QtCore

from PySide6.QtCore import QObject, QThread, Signal, Slot, QCoreApplication, Qt, QMetaObject, QTimer
import ccxt
import os
import json
import pandas as pd
import ta
from datetime import datetime
from dotenv import load_dotenv
from styles import get_global_widget_style, get_text_area_style, get_frame_style, get_combobox_style, recolor_icon, get_standard_button_style, get_separator_style, color_for_message
from nano_database import NanoTradingBotDatabase
from indicator import indicators
from reports import Reports
from nano_dashboard import TerminalWidget, NanoDashboard
from nano_processor import PriceWatcher
from nano_notification import alert_telegram, alert_email


class TradingWorker(QObject):
    update_status = Signal(str, str)
    update_terminal = Signal(str, str, str)
    trading_finished = Signal()
    update_exchanges_balances_signal = Signal()
    add_trade_signal = Signal(str, tuple, object)
    update_statistics_signal = Signal(dict)  # Défini en haut de la classe
    # Nouveaux signaux pour la barre de statut améliorée
    update_enhanced_status = Signal(str, str, dict, int, int, float)  # main_status, function, symbols_info, exchanges_count, trades_count, progress



    def __init__(self, config, price_watcher, dashboard, nano_database):
        super().__init__()
        self.config = config
        self.nano_processor = price_watcher  # Assurez-vous de passer et stocker l'objet nano_processor
        self.dashboard = dashboard  # Stocker une référence au dashboard
        self.nano_database = nano_database

        self.trading_active = False
        self.trade_statistics = {'buy': 0, 'sell': 0, 'no_action': 0}

        self.traded_symbols = {}
        #self.total_trade_count = 0
        #self.load_total_trade_count()

    @Slot()
    def start_trading(self):
        self.trading_active = True
        self.run_trading_logic()

    @Slot()
    def stop_trading(self):
        self.trading_active = False




    def run_trading_logic(self):

        #self.load_previous_trades()  # Charger les trades précédents au démarrage

        while self.trading_active:
            # Obtenir les échanges sélectionnés depuis les checkboxes
            selected_exchanges = [exchange for exchange, checkbox in self.config.nano_trading_exchange_vars.items() if checkbox.isChecked()]

            # Mettre à jour la liste des échanges sélectionnés dans la configuration
            self.config.nano_trading_selected_exchanges = selected_exchanges

            selected_currency = self.config.get_selected_currency()
            num_trades = int(self.config.num_trades_entry.text())
            trade_amount = float(self.config.trade_amount_entry.text())

            if not selected_currency:
                self.update_status.emit("Error", "No currency selected for trading.")
                self.update_enhanced_status.emit("❌ Erreur", "Configuration", {}, 0, 0, 0.0)
                return

            if not selected_exchanges:
                self.update_status.emit("Error", "No exchanges selected for trading.")
                self.update_enhanced_status.emit("❌ Erreur", "No Exchanges", {}, 0, 0, 0.0)
                return

            # Émettre le statut initial avec le bon nombre d'échanges
            exchanges_count = len(selected_exchanges)
            self.update_enhanced_status.emit("🔄 Trading Active", "Initialization", {}, exchanges_count, 0, 0.0)




            # Vérifier le nombre de trades actifs
            active_trades_count = self.get_active_trades_count()  # Récupérer le nombre actuel de trades actifs

            # Mettre à jour le statut avec le nombre de trades actifs
            self.update_enhanced_status.emit("🔄 Trading Active", "Checking Trades", {}, exchanges_count, active_trades_count, 0.0)

            # Si on a déjà atteint le nombre maximum de trades actifs, attendre que des positions se libèrent
            if active_trades_count >= num_trades:
                self.update_terminal.emit("Info", f"Maximum {num_trades} active trades reached.", "info")
                self.update_enhanced_status.emit("⏸️ Waiting", "Trade Limit Reached", {}, exchanges_count, active_trades_count, 100.0)
                # Attendre que des positions se libèrent
                while active_trades_count >= num_trades and self.trading_active :
                    QThread.sleep(5)
                    active_trades_count = self.get_active_trades_count()
                    self.update_enhanced_status.emit("⏸️ Waiting", "Awaiting Release", {}, exchanges_count, active_trades_count, 100.0)
                self.update_terminal.emit("Info", "Active trade slots available, continuing trading.", "info")
                continue  # Recommencer la boucle après avoir attendu


            # Sinon, continuer la recherche de nouvelles opportunités pour combler les trades manquants
            self.update_terminal.emit("Info", f"Only {active_trades_count} active trades. Searching for new trades.", "info")
            self.update_enhanced_status.emit("🔍 Searching", "New Opportunities", {}, exchanges_count, active_trades_count, 0.0)



            selected_timeframe_actions = self.config.get_selected_timeframe_actions()

            # Extraire uniquement les valeurs d'actions
            print("selected_timeframe_actions :",selected_timeframe_actions)
            """selected_timeframe_actions : [{'timeframe_depth_action': '15m', 'action': 'Buy'}, {'timeframe_depth_action': '1h', 'action': 'No Action'}]"""
            timeframe_depth_action = [item['timeframe_depth_action'] for item in selected_timeframe_actions]
            print("timeframe_depth_action :", timeframe_depth_action )
            """timeframe_depth_action : ['5m', '1h', '12h']"""
            selected_depth_action =  [item['action'] for item in selected_timeframe_actions]
            print("selected_depth_action: ", selected_depth_action)
            """selected_depth_action:  ['Buy', 'No Action', 'Buy']"""


            selected_timeframe = self.config.timeframe_combobox.currentText()

            selected_indicators = [indicator for indicator, var in self.config.indicator_vars.items() if var.isChecked()]

            # Log for user info
            self.update_terminal.emit("Info", f"Selected Exchanges: {selected_exchanges}", "info")
            self.update_terminal.emit("Info", f"Selected Currency: {selected_currency}", "info")
            self.update_terminal.emit("Info", f"Selected Timeframe: {selected_timeframe}", "info")
            #self.update_terminal.emit("Info", f"Timeframe sélectionné: {selected_timeframe}\nTimeframes Depth sélectionnés: {timeframe_depth_action}", "info")
            self.update_terminal.emit("Info", f"Selected Indicators: {selected_indicators}", "info")

            #excluded_coins = ['AEUR', 'DAI', 'EUR', 'EURPS', 'GBP', 'ACM', '1MBABYDOGE']
            #excluded_end_prefix = ['UP', 'DOWN']
            #excluded_start_prefix = '1000'

            # Récupérer les valeurs des tokens exclus et des préfixes
            excluded_coins = [token.strip().upper() for token in self.config.excluded_tokens_entry.text().split(',') if self.config.excluded_tokens_entry.text()]
            excluded_end_prefix = [prefix.strip().upper() for prefix in self.config.excluded_end_prefix_entry.text().split(',') if self.config.excluded_end_prefix_entry.text()]
            excluded_start_prefix = [prefix.strip().upper() for prefix in self.config.excluded_start_prefix_entry.text().split(',') if self.config.excluded_start_prefix_entry.text()]



            # Récupérer les indices de recherche
            start_index = int(self.config.search_index_start_entry.text()) if self.config.search_index_start_entry.text() else None
            end_index = int(self.config.search_index_end_entry.text()) if self.config.search_index_end_entry.text() else None



            total_exchanges = len(selected_exchanges)
            for exchange_idx, exchange_name in enumerate(selected_exchanges):
                print(f"Analyse de l'échange: {exchange_name}")  # Log pour chaque échange

                # Calculer la progression des exchanges
                exchange_progress = (exchange_idx / total_exchanges) * 100
                self.update_enhanced_status.emit("🔍 Analysis", f"Exchange: {exchange_name}", {}, exchanges_count, active_trades_count, exchange_progress)

                exchange = getattr(ccxt, exchange_name)({
                    'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                    'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                    'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
                })

                try:
                    exchange.load_markets()
                    self.update_terminal.emit("Info", f"Markets loaded for {exchange_name}", "info")
                    self.update_enhanced_status.emit("📊 Loading", f"Markets: {exchange_name}", {}, exchanges_count, active_trades_count, exchange_progress)

                    trade_scores = []

                    pairs = [
                        symbol for symbol in exchange.symbols
                        if symbol.endswith(f'/{selected_currency}')
                        and not any(symbol.endswith(f'{end}/{selected_currency}') for end in excluded_end_prefix)
                        and not any(symbol.startswith(f'{start}') for start in excluded_start_prefix)
                        and not any(stable in symbol for stable in excluded_coins)
                    ]

                    # Filtrage des paires en fonction des indices fournis
                    if start_index is not None and end_index is not None:
                        pairs = pairs[start_index:end_index]
                    elif start_index  and end_index is  None:
                        pairs = pairs[start_index:]  # Utiliser tous les éléments à partir de start_index
                    elif end_index and start_index is None:
                        pairs = pairs[:end_index]  # Utiliser tous les éléments jusqu'à end_index
                    elif start_index is None and end_index is  None:
                        pairs = pairs[:]

                    # Récupérer les paramètres de filtrage de liquidité depuis l'interface
                    min_volume = float(self.config.min_volume_entry.text()) if self.config.min_volume_entry.text() else self.config.min_volume_usdt
                    max_spread = float(self.config.max_spread_entry.text()) if self.config.max_spread_entry.text() else self.config.max_spread_pct

                    # Filtrage de liquidité - Étape préliminaire
                    self.update_terminal.emit("Liquidity Filter", f"Filtering {len(pairs)} symbols for liquidity...", "info")
                    liquid_pairs = self.config.filter_liquid_symbols(exchange, pairs, min_volume, max_spread)
                    self.update_terminal.emit("Liquidity Filter", f"Found {len(liquid_pairs)} liquid symbols out of {len(pairs)}", "info")

                    # Utiliser les paires filtrées pour l'analyse
                    pairs = liquid_pairs

                    # Étape 1: Évaluer chaque paire sur le timeframe principal
                    total_pairs = len(pairs)
                    for idx, pair in enumerate(pairs):

                        if not self.trading_active:
                            break
                        # Progression
                        progress = (idx + 1) / total_pairs * 100
                        symbols_info = {'current': idx + 1, 'total': total_pairs}

                        # Mettre à jour la barre de statut avec les informations détaillées
                        self.update_enhanced_status.emit(
                            "📈 Analysis",
                            f"Symbol: {pair}",
                            symbols_info,
                            exchanges_count,
                            active_trades_count,
                            progress
                        )

                        self.update_terminal.emit("Loading...", f"{idx + 1}/{total_pairs} : {pair} ({progress:.2f}%)", "info")

                        # Récupérer les données sur le timeframe sélectionné
                        df = self.config.fetch_data(exchange, pair, selected_timeframe)
                        if df is None:
                            continue
                        self.config.calculate_indicators(df)


                        # Évaluer le marché et calculer le score
                        self.update_enhanced_status.emit(
                            "🧮 Evaluation",
                            f"Analysis: {pair}",
                            symbols_info,
                            exchanges_count,
                            active_trades_count,
                            progress
                        )

                        action, score = self.config.evaluate_market_and_trade(df, selected_indicators, self.config.dashboard, self.config.threshold, selected_timeframe, pair)

                        if action is not None:

                            # Calcul des statistiques pour tous les indicateurs
                            all_indicator_stats, all_type_stats = self.calculate_all_indicator_stats(indicators, action)

                            # Calcul des statistiques pour les indicateurs sélectionnés
                            selected_indicator_stats, selected_type_stats = self.calculate_selected_indicator_stats(selected_indicators, all_indicator_stats, action)

                            # Données supplémentaires pour la base de données
                            #current_price = df['close'].iloc[-1]  # Dernier prix de clôture comme prix actuel

                            if len(df) > 0:
                                current_price = df['close'].iloc[-1]
                            else:
                                self.update_terminal.emit("Error", f"No data available for {pair} in timeframe {selected_timeframe}", "error")
                                continue

                            threshold = self.config.threshold

                            # Préparation des données à insérer dans la base de données
                            market_stats = {
                                'exchange': exchange_name,
                                'pair': pair,
                                'timeframe': selected_timeframe,
                                'action': action,
                                'score': score,

                                # Statistiques pour tous les indicateurs
                                'all_indicators': ', '.join([f"{ind}: {all_indicator_stats[ind]['category']}" for ind in all_indicator_stats]),
                                'all_indicators_stats': json.dumps(all_indicator_stats),
                                'all_indicators_type_stats': json.dumps(all_type_stats),

                                # Statistiques pour les indicateurs sélectionnés
                                'selected_indicators': ', '.join([f"{ind}: {selected_indicator_stats[ind]['category']}" for ind in selected_indicator_stats]),
                                'selected_indicators_stats': json.dumps(selected_indicator_stats),
                                'selected_indicators_type_stats': json.dumps(selected_type_stats),

                                # Valeurs de prix et seuil
                                'current_price': current_price,
                                'threshold': threshold,
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }

                            # Insertion dans la base de données
                            self.nano_database.update_market_statistics(market_stats)

                        # Enregistrement des statistiques de trading
                        trade_stats = {
                            'exchange': exchange_name,
                            'pair': pair,
                            'timeframe': selected_timeframe,
                            'current_buy': 1 if action == 'buy' else 0,
                            'current_sell': 1 if action == 'sell' else 0,
                            'current_no_action': 1 if action == 'no_action' else 0,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        self.nano_database.update_trade_statistics(trade_stats)
                        self.update_statistics_signal.emit(trade_stats)  # Mettre à jour l'interface utilisateur avec les statistiques actuelles


                        selected_action = self.config.main_action_combobox.currentText()
                        print("selected_action: ", selected_action)
                        if selected_action == "No Action":
                                selected_action = "no_action"

                        if action == selected_action.lower(): #'sell','No Action':

                            trade_scores.append((pair, action, score))
                            self.update_terminal.emit("Trade Score ", str(trade_scores), "info")

                    # Étape 2: Trier les paires en fonction de leurs scores
                    trade_scores.sort(key=lambda x: x[2])#, reverse=True)

                    # Étape 3: Pour chaque paire avec un bon score, évaluer les timeframes de profondeur
                    for pair, action, score in trade_scores:
                        if ((action == 'buy' and score <= 10) or ( action == 'sell' and score >= -10 ) or ( action == 'no_action' and score > 0)):
                            combined_scores = []
                            # Boucle à travers chaque dictionnaire dans la liste
                            for item in selected_timeframe_actions:
                                depth_timeframe = item['timeframe_depth_action']  # Récupérer le timeframe
                                selected_depth_action = item['action']  # Récupérer l'action

                                df_depth = self.config.fetch_data(exchange, pair, depth_timeframe)
                                if df_depth is None:
                                    self.update_terminal.emit("Error", f"No data for {pair} in the timeframe {selected_timeframe}")
                                    continue

                                self.config.calculate_indicators(df_depth)

                                # Évaluer sur le timeframe de profondeur
                                depth_action, depth_score = self.config.evaluate_market_and_trade(df_depth, selected_indicators, self.config.dashboard, self.config.threshold, depth_timeframe, pair)

                                # Récupérer l'action spécifique sélectionnée pour ce timeframe de profondeur
                                #print("depth_selected_action: ",selected_depth_action)

                                # Ajouter au score combiné uniquement si l'action est un 'buy'

                                if selected_depth_action == "No Action":
                                    selected_depth_action = "no_action"

                                if depth_action == selected_depth_action.lower():
                                    combined_scores.append((depth_timeframe, depth_score))
                                    self.update_terminal.emit("Info", f"'{selected_depth_action}' found for {pair} in the timeframe {depth_timeframe}", "info")
                                #else:
                                    #self.update_terminal.emit("Info", f"Aucune action 'buy' trouvée pour {pair} dans le timeframe {depth_timeframe}", "info")

                            # Étape 4: Calculer le score pondéré avec les résultats des timeframes de profondeur
                            weighted_score = self.config.calculate_weighted_score(combined_scores)

                            # Étape 5: Exécuter un trade si le score pondéré est satisfaisant
                            if weighted_score is not None and pair not in self.config.unique_traded_pairs:
                                self.update_terminal.emit("Info", f"Weighted Score for {pair} with Depth Action '{depth_action}' is: {weighted_score} ", "info")
                                self.execute_initial_trade(exchange, pair, 'buy', trade_amount, num_trades, weighted_score)
                                # Vérifier si on a atteint la limite de trades actifs

                                # Mettre à jour le nombre de trades actifs
                                active_trades_count = self.get_active_trades_count()
                                if active_trades_count >= num_trades:
                                    print(f"self.config.unique_traded_pairs: {self.config.unique_traded_pairs}")
                                    print(f"self.config.num_trades: {num_trades}")

                                    self.update_terminal.emit("Info", "Trade limit reached, waiting for slots to free up.", "info")
                                    # Attendre que des positions se libèrent
                                    while active_trades_count >= num_trades and self.trading_active and pair not in self.nano_database.get_all_trades():
                                        QThread.sleep(5)
                                        active_trades_count = self.get_active_trades_count()
                                        print(f"Active Trades: {active_trades_count}/{num_trades}")
                                    self.update_terminal.emit("Info", "Active trade slots available, continuing trading.", "info")
                                    break  # Sortir de la boucle pour recommencer

                except Exception as e:
                    self.update_status.emit("Error", f"Error loading markets for {exchange_name}: {e}")

            # Fin du cycle d'analyse, attente avant le prochain cycle
            self.update_enhanced_status.emit("⏳ Waiting", "Next Cycle", {}, exchanges_count, active_trades_count, 100.0)
            QThread.sleep(3)

        # Trading terminé
        self.update_enhanced_status.emit("⏹️ Stopped", "Trading Finished", {}, 0, 0, 0.0)
        self.trading_finished.emit()



    def get_active_trades_count(self):
        """Retourne le nombre de trades actifs."""
        active_trades = self.nano_database.get_all_trades()  # Récupère les trades actifs depuis la base de données
        return len(active_trades)




    def calculate_all_indicator_stats(self,indicators, action):
        """Calcule les statistiques pour tous les indicateurs."""
        all_indicator_stats = {}
        all_type_stats = {}

        # Initialisation des statistiques pour tous les indicateurs avec les noms de catégories
        for category, indicators_list in indicators.items():
            for ind in indicators_list:
                all_indicator_stats[ind] = {
                    'category': category,
                    'stats': {'buy': 0, 'sell': 0, 'no_action': 0}
                }

            # Initialisation des statistiques pour chaque catégorie
            all_type_stats[category] = {'buy': 0, 'sell': 0, 'no_action': 0}

        # Calcul des statistiques pour tous les indicateurs
        for indicator in all_indicator_stats.keys():
            if action == 'buy':
                all_indicator_stats[indicator]['stats']['buy'] += 1
            elif action == 'sell':
                all_indicator_stats[indicator]['stats']['sell'] += 1
            else:
                all_indicator_stats[indicator]['stats']['no_action'] += 1

            # Mise à jour des statistiques par catégorie
            category = all_indicator_stats[indicator]['category']
            if action == 'buy':
                all_type_stats[category]['buy'] += 1
            elif action == 'sell':
                all_type_stats[category]['sell'] += 1
            else:
                all_type_stats[category]['no_action'] += 1

        return all_indicator_stats, all_type_stats


    def calculate_selected_indicator_stats(self,selected_indicators, all_indicator_stats, action):
        """Calcule les statistiques pour les indicateurs sélectionnés."""
        selected_indicator_stats = {}
        selected_type_stats = {}

        for indicator in selected_indicators:
            selected_indicator_stats[indicator] = {
                'category': all_indicator_stats[indicator]['category'],
                'stats': {'buy': 0, 'sell': 0, 'no_action': 0}
            }

            if action == 'buy':
                selected_indicator_stats[indicator]['stats']['buy'] += 1
            elif action == 'sell':
                selected_indicator_stats[indicator]['stats']['sell'] += 1
            else:
                selected_indicator_stats[indicator]['stats']['no_action'] += 1

            # Mise à jour des statistiques par catégorie pour les indicateurs sélectionnés
            category = selected_indicator_stats[indicator]['category']
            if category not in selected_type_stats:
                selected_type_stats[category] = {'buy': 0, 'sell': 0, 'no_action': 0}

            if action == 'buy':
                selected_type_stats[category]['buy'] += 1
            elif action == 'sell':
                selected_type_stats[category]['sell'] += 1
            else:
                selected_type_stats[category]['no_action'] += 1

        return selected_indicator_stats, selected_type_stats



    def execute_initial_trade(self, exchange, symbol, action, trade_amount, num_of_trades, score):

        try:
            # Émettre le statut d'exécution du trade
            self.update_enhanced_status.emit(
                "💰 Execution",
                f"Trade {action.upper()}: {symbol}",
                {},
                0,
                0,
                0.0
            )

            # Récupérer les informations de ticker pour le symbole
            ticker = exchange.fetch_ticker(symbol)
            last_close = ticker['last']
            if exchange.name == 'mexc'.lower():
                exchange.options['createMarketBuyOrderRequiresPrice'] = False
            if action == 'buy':
                buy_price = last_close


            # Calculer le montant à trader basé sur le prix actuel
            amount_to_trade = trade_amount / last_close

            # Récupérer l'ID du trade existant, si disponible
            existing_trade_id = None  # Variable pour l'ID existant si trouvé
            existing_trade = self.nano_database.get_trade_by_symbol_and_exchange(symbol, exchange.name.lower())
            if existing_trade:
                existing_trade_id = existing_trade['id']

            # Déterminer le mode de calcul du prix d'achat
            profit_mode = self.config.get_profit_calculation_mode()
            
            if profit_mode == 'last_buy_price':
                buy_price = self.config.calculate_last_buy_price(symbol, existing_trade_id, last_close)
                self.update_terminal.emit("Info", f"Using Last Buy Price mode for {symbol}", "info")
            else:  # Mode average_buy_price par défaut
                buy_price = self.config.calculate_average_buy_price(symbol, existing_trade_id, last_close, amount_to_trade)
                self.update_terminal.emit("Info", f"Using Average Buy Price mode for {symbol}", "info")

            self.update_terminal.emit("Info", f"Placing {action} order for {amount_to_trade} {symbol} at price {last_close}", "info")

            # Passer l'ordre
            order = exchange.createOrder(symbol, 'market', action, amount_to_trade)
            
            # Récupérer le prix d'achat réel après l'exécution de l'ordre
            actual_buy_price = None
            if 'price' in order and order['price'] is not None:
                actual_buy_price = float(order['price'])
                self.update_terminal.emit("Info", f"Prix d'achat réel pour {symbol}: {actual_buy_price}", "info")
            else:
                # Si le prix n'est pas dans la réponse, essayer de récupérer l'ordre
                try:
                    order_id = order.get('id')
                    if order_id:
                        order_info = exchange.fetch_order(order_id, symbol)
                        if 'price' in order_info and order_info['price'] is not None:
                            actual_buy_price = float(order_info['price'])
                            self.update_terminal.emit("Info", f"Prix d'achat réel (récupéré via fetch_order) pour {symbol}: {actual_buy_price}", "info")
                except Exception as e:
                    print(f"Erreur lors de la récupération du prix d'achat réel: {e}")
            
            # Si on n'a pas pu récupérer le prix réel, utiliser le dernier prix connu
            if actual_buy_price is None:
                actual_buy_price = last_close
                self.update_terminal.emit("Warning", f"Impossible de récupérer le prix d'achat réel pour {symbol}, utilisation du dernier prix: {last_close}", "warning")

            self.update_terminal.emit("Info", f"{action.capitalize()} order executed for {symbol}: {order}", "info")
            try:
                alert_telegram(f"{datetime.now().strftime('%Hh:%Mmin | %d-%m-%Y')}: {action.capitalize()} order executed for {symbol} at {actual_buy_price}\n{os.getenv('SERVER_NAME')}")
                alert_email(f"{datetime.now().strftime('%Hh:%Mmin | %d-%m-%Y')}: {action.capitalize()} order executed for {symbol} at {actual_buy_price}\n{os.getenv('SERVER_NAME')}")
            except Exception as e:
                print(f" {e}")

            # Calculer le pourcentage du trade avec le prix d'achat réel
            trade_percent = ((last_close - actual_buy_price) / actual_buy_price) * 100



            # Calculer les prix d'achat pour chaque ligne avant de stocker les données
            for row in self.config.buy_rows:
                row['symbol'] = symbol
                row['exchange'] = exchange.name
                row['price_to_buy'] = self.config.calculate_price_to_buy(row, actual_buy_price)

            # Calculer les prix de vente avec le prix d'achat réel
            for row in self.config.sell_rows:
                row['symbol'] = symbol
                row['exchange'] = exchange.name
                # Utiliser l'ID du trade existant s'il est disponible, sinon None
                # Passer le prix d'achat réel pour un calcul précis du prix de vente
                row['price_to_sell'] = self.config.calculate_price_to_sell(row, actual_buy_price, existing_trade_id, actual_buy_price)
                
                # Si le prix de vente est None (cas du mode Fixed Profit sans prix réel), on le calcule maintenant
                if row['price_to_sell'] is None and self.config.get_profit_calculation_mode() == 'fixed_profit':
                    row['price_to_sell'] = self.config.calculate_fixed_profit_sell_price(actual_buy_price, existing_trade_id)
                    if row['price_to_sell'] is not None:
                        self.update_terminal.emit("Info", f"Prix de vente calculé en mode Fixed Profit: {row['price_to_sell']} (basé sur le prix d'achat réel: {actual_buy_price})", "info")

            # Sérialiser les configurations des buy_rows et sell_rows
            serialized_buy_rows = [
                {
                    "percentage_combobox": row["percentage_combobox"].currentText(),  # Extraire le texte sélectionné
                    "amount_entry": row["amount_entry"].text(),  # Extraire le texte de l'entrée
                    "price_to_buy": row["price_to_buy"],
                    "symbol": symbol,
                    "exchange": exchange.name
                }
                for row in self.config.buy_rows
            ]

            serialized_sell_rows = [
                {
                    "percentage_combobox": row["percentage_combobox"].currentText(),  # Extraire le texte sélectionné
                    "sell_percentage_entry": row["sell_percentage_entry"].text(),  # Extraire le texte de l'entrée
                    "price_to_sell": row["price_to_sell"],
                    "symbol": symbol,
                    "exchange": exchange.name
                }
                for row in self.config.sell_rows
            ]

            # L'ID sera généré automatiquement par la base de données (AUTOINCREMENT)




            # Préparer les données du trade (sans ID car la DB l'auto-génère)
            trade_data = {
                'exchange': exchange.name.lower(),
                'symbol': symbol,
                'timeframe': self.config.timeframe_combobox.currentText(),
                'base_token': symbol.split('/')[0],  # Exemple : ALCX dans ALCX/USDT
                'quote_token': symbol.split('/')[1],  # Exemple : USDT dans ALCX/USDT
                'amount_to_trade': amount_to_trade,
                'trade_amount': trade_amount,
                'buy_price': buy_price,
                'current_price': last_close,
                'trade_percent': trade_percent,
                'buy_rows': json.dumps(serialized_buy_rows),  # Sérialiser buy_rows en JSON
                'sell_rows': json.dumps(serialized_sell_rows),  # Sérialiser sell_rows en JSON
                'last_action': action,
                'trade_situation': 'in process',
                'buy_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'last_action_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),



            }

            # Insérer les données dans la base de données et récupérer l'ID réel généré
            trade_id = self.nano_database.insert_trade(trade_data)

            # Émettre le signal pour mettre à jour l'interface utilisateur avec le nouveau trade
            trade_details = (
                trade_id,                  # ID du trade
                exchange.name.lower(),
                symbol,
                self.config.timeframe_combobox.currentText(),
                symbol.split('/')[0],
                amount_to_trade,
                symbol.split('/')[1],
                trade_amount,
                buy_price,
                last_close,
                trade_percent,
                action,
                'in process',
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),

            )

            self.add_trade_signal.emit(exchange.name, trade_details, self.dashboard)

            # Mise à jour des soldes après la transaction
            self.update_exchanges_balances_signal.emit()

            # Ajouter le trade au set unique_traded_pairs pour suivi
            self.config.add_unique_trade_pair(symbol)

        except Exception as e:
            #self.update_status.emit("Error", f"Error executing trade for {symbol}: {e}")
            #alert_telegram(f"Error executing trade for {symbol}: {e}")
            print(f"Error executing trade for {symbol}: {e}")




class NanoConfiguration(QObject):  # Héritez de QObject pour pouvoir utiliser les signaux
    currency_selected_signal = Signal()


    def __init__(self):
        super().__init__()  # Appel correct à super()


        self.indicators = indicators
        self.trading_thread = None
        self.trading_worker = None
        self.nano_trading_active = False
        self.trade_amount = 10
        self.num_trades = 3

        self.excluded_tokens_list = ['AEUR', 'DAI', 'EUR', 'EURPS', 'GBP', 'ACM', '1MBABYDOGE', 'ASTR', 'ARDR', 'ASR', 'ACA', 'FDUSD',  'DOT3S']
        self.excluded_end_prefix_list = ['UP', 'DOWN']
        self.excluded_start_prefix_list = ['1000']
        self.search_index_start = 0
        self.search_index_end = 250

        self.threshold = 0.369

        # Paramètres de filtrage de liquidité
        self.min_volume_usdt = 1000000  # Volume minimum en USDT
        self.max_spread_pct = 0.1       # Spread maximum en pourcentage

        self.balance_initial_by_currency = {
            'USDT': 0,
            'USDC': 0,
            'BTC': 0,
        }
        self.nano_trading_exchange_vars = {}
        self.nano_trading_selected_exchanges = []
        self.timeframe_checkboxes = {}
        self.usdt_checkbox = None
        self.usdc_checkbox = None
        self.btc_checkbox = None
        self.custom_symbol_entry = None
        self.timeframe_combobox = None
        self.trade_amount_entry = None
        self.num_trades_entry = None
        self.threshold_entry = None
        self.min_volume_entry = None
        self.max_spread_entry = None
        self.buy_rows = []
        self.sell_rows = []
        self.start_button = None
        self.stop_button = None

        self.previous_buy_price = None
        self.previous_sell_price = None
        self.loaded_depth_timeframes = []
        self.db_path = "nano_trading_bot.db"  # Remplacez par le chemin de votre base de données

        # Vérifier et supprimer l'ancienne base de données si elle existe
        self.check_and_delete_old_database()
        self.db = NanoTradingBotDatabase()
        self.reports = Reports()
        self.status_bar = None
        self.nano_processor = PriceWatcher(self, self.db, None)  # Passer None pour le dashboard pour l'instant
        self.dashboard = NanoDashboard(self.status_bar, self)

        #self.dashboard = None
        self.nano_processor.dashboard = self.dashboard

        self.setup_configuration()
        self.connected_exchanges_widget = None  # Ajouter une référence pour ConnectedExchangesBalancesWidget

        self.unique_traded_pairs = set()


        # Connecter le signal à la méthode qui vérifie et redémarre le bot
        self.nano_processor.worker.restart_trading_signal.connect(self.restart_trading)
        # Connect application quit signal to cleanup method
        QCoreApplication.instance().aboutToQuit.connect(self.on_application_exit)

        self.trading_worker = TradingWorker(self, self.nano_processor, self.dashboard, self.db)

        # Connexion du signal update_statistics_signal au widget
        self.trading_worker.update_statistics_signal.connect(
            self.dashboard.exchange_symbol_trade_data_widget.update_widget_content
        )


    def check_and_delete_old_database(self):
        """Vérifier et supprimer l'ancienne base de données si elle existe."""
        if os.path.exists(self.db_path):
            try:
                os.remove(self.db_path)
                print(f"Old database deleted : {self.db_path}")
            except Exception as e:
                print(f"Unable to delete old database : {self.db_path}. Error : {e}")
        else:
            print(f"No old databases found at : {self.db_path}")



    def restart_trading(self):
        # Arrêter le trading actuel si nécessaire
        self.stop_nano_trading()
        # Redémarrer le processus de recherche de nouveaux symboles
        self.start_nano_trading()


    def setup_configuration(self):
        self.status_bar = self.create_status_bar()
        if not hasattr(self, 'dashboard') or self.dashboard is None:
            self.dashboard = NanoDashboard(self.status_bar, self)



    def __del__(self):
        """try:
            if hasattr(self, 'dashboard') and self.dashboard:
                self.stop_nano_trading()
        except AttributeError:
            print("Le tableau de bord n'est pas défini ou déjà supprimé.")"""
        pass

    def set_dashboard(self, dashboard):
        self.dashboard = dashboard
        self.connected_exchanges_widget = dashboard.connected_exchanges_widget  # Lier le widget ConnectedExchangesBalancesWidget

    def create_status_bar(self):
        """Crée et configure une barre d'état."""
        status_bar = QStatusBar()

        # Ajouter un label à la barre d'état pour afficher des messages
        self.status_message = QLabel("Prêt")  # Message par défaut
        status_bar.addPermanentWidget(self.status_message)

        return status_bar

    def display_to_nano_terminal(self, title, message, level):
        if self.dashboard:
            self.dashboard.display_message_to_terminal(title, message, level)

    def display_to_status_bar(self, title, message):
        if hasattr(self, 'status_bar') and self.status_bar:
            self.status_bar.showMessage(f"{title}: {message}", 3000)

    def update_enhanced_status_bar(self, main_status, function, symbols_info, exchanges_count, trades_count, progress):
        """Met à jour la barre de statut améliorée avec les informations détaillées du trading"""
        if hasattr(self, 'main_window') and self.main_window:
            # Utiliser la référence directe à la fenêtre principale
            if hasattr(self.main_window, 'update_enhanced_status'):
                self.main_window.update_enhanced_status(
                    main_status=main_status,
                    function=function,
                    symbols_info=symbols_info,
                    exchanges_count=exchanges_count,
                    trades_count=trades_count,
                    progress=progress
                )

    def add_separator(self, scroll_layout, row):
        """
        Ajoute un séparateur avec espacement dans le layout donné.
        """
        # Ajouter un espace avant le séparateur
        scroll_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        # Créer le séparateur (ligne horizontale)
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName('separator')
        separator.setStyleSheet(get_separator_style(True))

        # Ajouter le séparateur au layout
        scroll_layout.addWidget(separator, row, 0, 1, 3)
        row += 1

        # Ajouter un espace après le séparateur
        scroll_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        return row




    def create_configuration_widget(self):
        load_dotenv()
        widget = QWidget()
        layout = QVBoxLayout(widget)
        widget.setStyleSheet("background-color: rgba(100, 100, 100, 0.05); border-radius: 10px;")

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setObjectName('scrollAreaStyle')
        scroll_bar = QScrollBar()
        scroll_bar.setObjectName('scrollBarStyle')

        scroll_content = QWidget()
        scroll_layout = QGridLayout(scroll_content)
        scroll_content.setStyleSheet(get_global_widget_style())

        exchanges_list = [exchange for exchange in ccxt.exchanges if os.getenv(f'{exchange.upper()}_API_KEY') and os.getenv(f'{exchange.upper()}_SECRET_KEY')]

        row = 0
        scroll_layout.addWidget(QLabel("Select Exchanges:"), row, 0, 1, 2)
        row += 1
        exchange_col = 0
        for exchange in exchanges_list:
            checkbox = QCheckBox(exchange)
            self.nano_trading_exchange_vars[exchange] = checkbox
            scroll_layout.addWidget(checkbox, row, exchange_col)

            # Connecter chaque checkbox d'exchange pour mettre à jour les balances disponibles et le texte des tokens
            checkbox.stateChanged.connect(lambda state, ex=exchange: self.check_exchange_balance(ex))
            exchange_col += 1
            if exchange_col >= 2:
                exchange_col = 0
                row += 1
        row += 1


        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        scroll_layout.addWidget(QLabel("Trade Amount ($):"), row, 0)
        scroll_layout.addWidget(QLabel("Number of Trades:"), row, 1)
        row += 1
        self.trade_amount_entry = QLineEdit()
        self.trade_amount_entry.setPlaceholderText("Enter trade amount")
        self.trade_amount_entry.setObjectName("customLineEdit")
        self.trade_amount_entry.setStyleSheet(get_text_area_style(True))
        self.trade_amount_entry.setText(str(self.trade_amount))
        scroll_layout.addWidget(self.trade_amount_entry, row, 0)

        self.num_trades_entry = QLineEdit()
        self.num_trades_entry.setPlaceholderText("Enter number of trades")
        self.num_trades_entry.setObjectName("customLineEdit")
        self.num_trades_entry.setStyleSheet(get_text_area_style(True))
        self.num_trades_entry.setText(str(self.num_trades))
        scroll_layout.addWidget(self.num_trades_entry, row, 1)
        row += 1

         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1


        scroll_layout.addWidget(QLabel("Select Symbols:"), row, 0)
        row += 1
        symbols_layout = QVBoxLayout()
        self.usdt_checkbox = QCheckBox("USDT")
        symbols_layout.addWidget(self.usdt_checkbox)
        self.usdc_checkbox = QCheckBox("USDC")
        symbols_layout.addWidget(self.usdc_checkbox)
        self.btc_checkbox = QCheckBox("BTC")
        symbols_layout.addWidget(self.btc_checkbox)
        symbols_widget = QWidget()
        symbols_widget.setLayout(symbols_layout)
        scroll_layout.addWidget(symbols_widget, row, 0)

        # Connecter les changements de checkboxes symboles pour mettre à jour le label des tokens
        self.usdt_checkbox.stateChanged.connect(self.update_tokens_info_label)
        self.usdt_checkbox.stateChanged.connect(self.on_currency_selected)
        self.usdc_checkbox.stateChanged.connect(self.update_tokens_info_label)
        self.usdc_checkbox.stateChanged.connect(self.on_currency_selected)
        self.btc_checkbox.stateChanged.connect(self.update_tokens_info_label)
        self.btc_checkbox.stateChanged.connect(self.on_currency_selected)

        row += 1

        scroll_layout.addWidget(QLabel("Custom Symbol:"), row, 0)
        row += 1
        self.custom_symbol_entry = QLineEdit()
        self.custom_symbol_entry.setPlaceholderText("Enter custom symbol")
        self.custom_symbol_entry.setObjectName("customLineEdit")
        self.custom_symbol_entry.setStyleSheet(get_text_area_style(True))
        scroll_layout.addWidget(self.custom_symbol_entry, row, 0)

        # Connecter le changement de texte de custom_symbol_entry à la fonction de mise à jour
        self.custom_symbol_entry.textChanged.connect(self.update_tokens_info_label)
        self.custom_symbol_entry.textChanged.connect(self.on_currency_selected)


        row += 1

        # Créer un QVBoxLayout pour afficher les QLabel au-dessus de chaque QLineEdit
        excluded_layout = QHBoxLayout()

        # Layout pour chaque paire QLabel/QLineEdit
        excluded_tokens_layout = QVBoxLayout()
        excluded_end_prefix_layout = QVBoxLayout()
        excluded_start_prefix_layout = QVBoxLayout()

        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        # Label et QLineEdit pour 'Excluded Tokens'
        excluded_tokens_label = QLabel("Excluded Tokens:")
        self.excluded_tokens_entry = QLineEdit()
        self.excluded_tokens_entry.setPlaceholderText("Enter tokens to exclude")
        self.excluded_tokens_entry.setObjectName("customLineEdit")
        self.excluded_tokens_entry.setStyleSheet(get_text_area_style(True))
        self.excluded_tokens_entry.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.excluded_tokens_entry.setText(', '.join(self.excluded_tokens_list))

        excluded_tokens_layout.addWidget(excluded_tokens_label)
        excluded_tokens_layout.addWidget(self.excluded_tokens_entry)

        # Label et QLineEdit pour 'Excluded End Prefix'
        excluded_end_prefix_label = QLabel("Excluded End Prefix:")
        self.excluded_end_prefix_entry = QLineEdit()
        self.excluded_end_prefix_entry.setPlaceholderText("Enter end prefixes to exclude")
        self.excluded_end_prefix_entry.setObjectName("customLineEdit")
        self.excluded_end_prefix_entry.setStyleSheet(get_text_area_style(True))
        self.excluded_end_prefix_entry.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.excluded_end_prefix_entry.setText(', '.join(self.excluded_end_prefix_list))

        excluded_end_prefix_layout.addWidget(excluded_end_prefix_label)
        excluded_end_prefix_layout.addWidget(self.excluded_end_prefix_entry)

        # Label et QLineEdit pour 'Excluded Start Prefix'
        excluded_start_prefix_label = QLabel("Excluded Start Prefix:")
        self.excluded_start_prefix_entry = QLineEdit()
        self.excluded_start_prefix_entry.setPlaceholderText("Enter start prefixes to exclude")
        self.excluded_start_prefix_entry.setObjectName("customLineEdit")
        self.excluded_start_prefix_entry.setStyleSheet(get_text_area_style(True))
        self.excluded_start_prefix_entry.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.excluded_start_prefix_entry.setText(', '.join(self.excluded_start_prefix_list))

        excluded_start_prefix_layout.addWidget(excluded_start_prefix_label)
        excluded_start_prefix_layout.addWidget(self.excluded_start_prefix_entry)

        # Ajouter chaque layout de section à excluded_layout
        excluded_layout.addLayout(excluded_tokens_layout)
        excluded_layout.addLayout(excluded_end_prefix_layout)
        excluded_layout.addLayout(excluded_start_prefix_layout)

        # Ajouter le layout vertical au scroll_layout
        scroll_layout.addLayout(excluded_layout, row, 0, 1, 3)
        row += 1

        # Définir all_timeframes avant son utilisation
        all_timeframes = ["1s", "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M", "3M"]

        # Ajout de l'affichage avant le texte "Search Index Range:"
        selected_exchanges = self.nano_trading_selected_exchanges
        selected_currency = self.get_selected_currency()
        tokens_info_text = self.get_tokens_information(selected_exchanges, selected_currency)


        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        # Créer le QLabel pour afficher les informations des tokens avec retour à la ligne
        self.tokens_info_label = QLabel(tokens_info_text)  # Stocker le label dans self pour mise à jour
        self.tokens_info_label.setWordWrap(True)  # Activer le retour à la ligne automatique
        self.tokens_info_label.setStyleSheet("font-size: 12px; font-weight: bold; color: black;")  # Style du texte

        # Ajouter le QLabel au layout avant "Search Index Range:"
        scroll_layout.addWidget(self.tokens_info_label, row, 0, 1, 3)
        row += 1  # Incrémenter la ligne pour la prochaine section

        # Ajouter les QLineEdit pour la plage de recherche
        scroll_layout.addWidget(QLabel("Search Index Range:"), row, 0, 1, 3)
        row += 1

        index_range_layout = QHBoxLayout()

        self.search_index_start_entry = QLineEdit()
        self.search_index_start_entry.setPlaceholderText("Start index (e.g., 0)")
        self.search_index_start_entry.setObjectName("customLineEdit")
        self.search_index_start_entry.setStyleSheet(get_text_area_style(True))
        self.search_index_start_entry.setText(str(self.search_index_start))
        index_range_layout.addWidget(self.search_index_start_entry)

        self.search_index_end_entry = QLineEdit()
        self.search_index_end_entry.setPlaceholderText("End index (e.g., 50)")
        self.search_index_end_entry.setObjectName("customLineEdit")
        self.search_index_end_entry.setStyleSheet(get_text_area_style(True))
        self.search_index_end_entry.setText(str(self.search_index_end))
        index_range_layout.addWidget(self.search_index_end_entry)

        scroll_layout.addLayout(index_range_layout, row, 0, 1, 3)
        row += 1


         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        scroll_layout.addWidget(QLabel("Select Timeframe:"), row, 0)
        scroll_layout.addWidget(QLabel("Threshold:"), row, 1)
        row += 1
        self.timeframe_combobox = QComboBox()
        self.timeframe_combobox.setObjectName("customComboBox")
        self.timeframe_combobox.addItems(all_timeframes)
        self.timeframe_combobox.setStyleSheet(get_combobox_style(True))
        scroll_layout.addWidget(self.timeframe_combobox, row, 0, 1, 1)

        # Ajouter un combobox pour sélectionner l'action principale (Buy, Sell, No Action)

        self.threshold_entry = QLineEdit()
        self.threshold_entry.setPlaceholderText("Enter threshold")
        self.threshold_entry.setObjectName("customLineEdit")
        self.threshold_entry.setStyleSheet(get_text_area_style(True))
        self.threshold_entry.setText(str(self.threshold))
        scroll_layout.addWidget(self.threshold_entry, row, 1, 1, 1)
        row += 1

        # Ajouter les paramètres de filtrage de liquidité
        scroll_layout.addWidget(QLabel("Liquidity Filtering:"), row, 0, 1, 2)
        row += 1

        # Volume minimum
        scroll_layout.addWidget(QLabel("Min Volume (USDT):"), row, 0, 1, 1)
        self.min_volume_entry = QLineEdit()
        self.min_volume_entry.setPlaceholderText("Minimum volume in USDT")
        self.min_volume_entry.setObjectName("customLineEdit")
        self.min_volume_entry.setStyleSheet(get_text_area_style(True))
        self.min_volume_entry.setText(str(self.min_volume_usdt))
        scroll_layout.addWidget(self.min_volume_entry, row, 1, 1, 1)
        row += 1

        # Spread maximum
        scroll_layout.addWidget(QLabel("Max Spread (%):"), row, 0, 1, 1)
        self.max_spread_entry = QLineEdit()
        self.max_spread_entry.setPlaceholderText("Maximum spread percentage")
        self.max_spread_entry.setObjectName("customLineEdit")
        self.max_spread_entry.setStyleSheet(get_text_area_style(True))
        self.max_spread_entry.setText(str(self.max_spread_pct))
        scroll_layout.addWidget(self.max_spread_entry, row, 1, 1, 1)
        row += 1

         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        scroll_layout.addWidget(QLabel("Select Timeframes Depth:"), row, 0, 1, 2)
        row += 1
        self.timeframe_checkboxes_frame = QFrame()
        self.timeframe_checkboxes_frame.setStyleSheet(get_frame_style(True))
        self.timeframe_checkboxes_layout = QGridLayout(self.timeframe_checkboxes_frame)
        scroll_layout.addWidget(self.timeframe_checkboxes_frame, row, 0, 1, 2)
        row += 1

        self.timeframe_checkboxes = {}
        col = 0
        depth_row = 0
        for timeframe in all_timeframes:
            checkbox = QCheckBox(timeframe)
            self.timeframe_checkboxes[timeframe] = checkbox
            self.timeframe_checkboxes_layout.addWidget(checkbox, depth_row, col)
            depth_row += 1
            if depth_row >= 5:
                depth_row = 0
                col += 1
        self.initialize_timeframe_checkboxes()

         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1


        # Section "Select Main Action" ajustée pour placer le ComboBox en dessous du label
        row += 1

        # Ajouter le label dans le scroll_layout
        label_main_action = QLabel("Select Main Action:")
        scroll_layout.addWidget(label_main_action, row, 0, 1, 3)

        # Incrémenter la ligne
        row += 1

        # Ajouter le ComboBox juste en dessous du label
        self.main_action_combobox = QComboBox()
        self.main_action_combobox.setObjectName("customComboBox")
        self.main_action_combobox.addItems(["Buy", "Sell", "No Action"])
        self.main_action_combobox.setCurrentIndex(0)  # Par défaut 'Buy'

        self.main_action_combobox.setMinimumWidth(80)  # Largeur adaptative
        self.main_action_combobox.setStyleSheet(get_combobox_style(True))
        scroll_layout.addWidget(self.main_action_combobox, row, 0, 1, 3)

        row += 1  # Incrémenter pour l'élément suivant





        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

       # Ajouter un label pour la sélection des actions des timeframes de profondeur
        label_timeframe_actions = QLabel("Select Timeframes Depth Actions:")
        scroll_layout.addWidget(label_timeframe_actions, row, 0, 1, 1)  # Placer le label dans la première colonne
        row += 1  # Incrémenter la ligne pour le prochain ajout



        # Créer un QGridLayout pour organiser dynamiquement les labels et comboboxes
        self.timeframe_action_grid = QGridLayout()
        scroll_layout.addLayout(self.timeframe_action_grid, row, 0, 1, 2)  # Ajouter le QGridLayout au scroll_layout
        row += 1  # Incrémenter pour laisser de l'espace après



        # Appel initial pour générer les comboboxes en fonction des cases cochées au démarrage
        #update_timeframe_actions()
        for checkbox in self.timeframe_checkboxes.values():
            checkbox.stateChanged.connect(self.update_timeframe_actions)

        # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1



        scroll_layout.addWidget(QLabel("Select Indicators:"), row, 0, 1, 3)
        row += 1
        indicators_frame = QFrame()
        indicators_frame.setStyleSheet(get_frame_style(True))
        indicators_layout = QGridLayout(indicators_frame)
        col = 0
        ind_row = 0
        self.indicator_vars = {}
        for category, indics in self.indicators.items():


            category_label = QLabel(category)
            category_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
            indicators_layout.addWidget(category_label, ind_row, 0, 1, 3)
            ind_row += 1

            for indicator in indics:

                checkbox = QCheckBox(indicator)
                checkbox.setChecked(True)
                self.indicator_vars[indicator] = checkbox
                indicators_layout.addWidget(checkbox, ind_row, col)
                col += 1
                if col >= 3:
                    col = 0
                    ind_row += 1
            col = 0
            ind_row += 1

        scroll_layout.addWidget(indicators_frame, row, 0, 1, 3)
        row += 1


         # Ajouter un séparateur
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Fixed), row, 0, 1, 3)
        row += 1

        scroll_layout.addWidget(QLabel("Buy / Sell Configuration:"), row, 0, 1, 2)
        row += 1




        # Pour la configuration d'achat
        self.buy_setup_layout = QVBoxLayout()
        buy_setup_frame = QFrame()
        buy_setup_frame.setStyleSheet(get_frame_style(True))
        buy_setup_frame.setLayout(self.buy_setup_layout)
        self.add_buy_row(self.buy_setup_layout)



        # Pour la configuration de vente
        self.sell_setup_layout = QVBoxLayout()
        sell_setup_frame = QFrame()
        sell_setup_frame.setStyleSheet(get_frame_style(True))
        sell_setup_frame.setLayout(self.sell_setup_layout)
        self.add_sell_row(self.sell_setup_layout)

        scroll_layout.addWidget(buy_setup_frame, row, 0)
        scroll_layout.addWidget(sell_setup_frame, row, 1)
        row += 1


        # Ajouter un layout pour les boutons d'enregistrement et d'ouverture
        file_button_frame = QFrame()
        file_button_frame.setStyleSheet(get_frame_style(True))
        file_button_layout = QHBoxLayout(file_button_frame)

        self.save_button = QPushButton()
        self.save_button.setIcon(QIcon("icons/save-icon.png"))  # Assurez-vous que l'icône existe
        self.save_button.setStyleSheet("border: none;")  # Pas de bordure pour un look d'icône
        self.save_button.setMinimumSize(35, 35)
        self.save_button.setMaximumSize(45, 45)
        self.save_button.setToolTip("Save Configuration to File")
        self.save_button.clicked.connect(self.save_configuration_to_file)

        self.open_button = QPushButton()
        self.open_button.setIcon(QIcon("icons/open-icon.png"))  # Assurez-vous que l'icône existe
        self.open_button.setStyleSheet("border: none;")
        self.open_button.setMinimumSize(35, 35)
        self.open_button.setMaximumSize(45, 45)
        self.open_button.setToolTip("Open Configuration File")
        self.open_button.clicked.connect(self.open_configuration_file)

        file_button_layout.addWidget(self.save_button)
        file_button_layout.addWidget(self.open_button)

        scroll_layout.addWidget(file_button_frame, row, 1, 1, 1, alignment=Qt.AlignRight)
        row += 1
        # Profit Mode Radio Buttons
        profit_mode_frame = QFrame()
        profit_mode_frame.setStyleSheet(get_frame_style(True))
        profit_mode_layout = QVBoxLayout(profit_mode_frame)

        profit_mode_label = QLabel("Profit Calculation Mode:")
        profit_mode_label.setStyleSheet("color: white; font-weight: bold; font-size: 12px;")
        profit_mode_layout.addWidget(profit_mode_label)

        # Créer un groupe de boutons pour les modes de profit
        self.profit_mode_group = QButtonGroup(self)
        
        radio_layout = QHBoxLayout()

        self.average_buy_price_radio = QRadioButton("Average Buy Price")
        self.average_buy_price_radio.setStyleSheet("color: white; font-size: 11px;")
        self.average_buy_price_radio.setChecked(True)  # Mode actuel par défaut
        self.profit_mode_group.addButton(self.average_buy_price_radio)

        self.last_buy_price_radio = QRadioButton("Last Buy Price")
        self.last_buy_price_radio.setStyleSheet("color: white; font-size: 11px;")
        self.profit_mode_group.addButton(self.last_buy_price_radio)

        self.fixed_profit_radio = QRadioButton("Fixed Profit")
        self.fixed_profit_radio.setStyleSheet("color: white; font-size: 11px;")
        self.profit_mode_group.addButton(self.fixed_profit_radio)

        radio_layout.addWidget(self.average_buy_price_radio)
        radio_layout.addWidget(self.last_buy_price_radio)
        radio_layout.addWidget(self.fixed_profit_radio)

        # Connecter les signaux des boutons radio
        self.average_buy_price_radio.toggled.connect(self.on_profit_mode_changed)
        self.last_buy_price_radio.toggled.connect(self.on_profit_mode_changed)
        self.fixed_profit_radio.toggled.connect(self.on_profit_mode_changed)

        profit_mode_layout.addLayout(radio_layout)

        # Fixed Profit Amount Section
        fixed_profit_layout = QHBoxLayout()

        fixed_profit_label = QLabel("Fixed Profit Amount:")
        fixed_profit_label.setStyleSheet("color: white; font-weight: bold; font-size: 11px;")
        fixed_profit_layout.addWidget(fixed_profit_label)

        self.fixed_profit_entry = QLineEdit()
        self.fixed_profit_entry.setPlaceholderText("0.15")
        self.fixed_profit_entry.setText("0.15")
        self.fixed_profit_entry.setStyleSheet(get_text_area_style(True))
        self.fixed_profit_entry.setMaximumWidth(100)
        self.fixed_profit_entry.setVisible(False)  # Caché par défaut
        self.fixed_profit_entry.textChanged.connect(self.on_fixed_profit_changed)
        fixed_profit_layout.addWidget(self.fixed_profit_entry)

        # Label pour afficher la devise sélectionnée
        self.currency_label = QLabel("USDT")  # Par défaut à USDT
        self.currency_label.setStyleSheet("color: white; font-size: 11px;")
        self.currency_label.setVisible(False)  # Caché par défaut
        fixed_profit_layout.addWidget(self.currency_label)

        fixed_profit_layout.addStretch()

        # Connecter le changement de devise pour mettre à jour le label
        self.usdt_checkbox.stateChanged.connect(self.update_currency_label)
        self.usdc_checkbox.stateChanged.connect(self.update_currency_label)
        self.btc_checkbox.stateChanged.connect(self.update_currency_label)
        self.custom_symbol_entry.textChanged.connect(self.update_currency_label)

        profit_mode_layout.addLayout(fixed_profit_layout)

        # Stocker les références pour pouvoir les masquer/afficher
        self.fixed_profit_label = fixed_profit_label
        self.fixed_profit_usdt_label = self.currency_label

        # Stop Loss Section
        stop_loss_main_layout = QVBoxLayout()

        # Créer un groupe de boutons pour le stop loss
        self.stop_loss_group = QButtonGroup(self)
        
        # Stop Loss Enable (utiliser QCheckBox pour permettre toggle on/off)
        self.stop_loss_enabled_radio = QCheckBox("Enable Stop Loss")
        self.stop_loss_enabled_radio.setStyleSheet("color: white; font-size: 11px;")
        self.stop_loss_enabled_radio.setChecked(False)  # Désactivé par défaut
        stop_loss_main_layout.addWidget(self.stop_loss_enabled_radio)
        # Ne pas ajouter le checkbox au stop_loss_group car il doit pouvoir être toggle

        # Stop Loss Mode Selection (visible seulement si activé)
        self.stop_loss_mode_frame = QFrame()
        stop_loss_mode_layout = QVBoxLayout(self.stop_loss_mode_frame)

        stop_loss_mode_radio_layout = QHBoxLayout()

        # Créer un groupe pour les options de mode de stop loss
        self.stop_loss_mode_group = QButtonGroup(self)
        
        self.stop_loss_with_buy_rows_radio = QRadioButton("With Buy Rows")
        self.stop_loss_with_buy_rows_radio.setStyleSheet("color: white; font-size: 10px;")
        self.stop_loss_with_buy_rows_radio.setChecked(True)
        self.stop_loss_mode_group.addButton(self.stop_loss_with_buy_rows_radio)

        self.stop_loss_without_buy_rows_radio = QRadioButton("Without Buy Rows")
        self.stop_loss_without_buy_rows_radio.setStyleSheet("color: white; font-size: 10px;")
        self.stop_loss_mode_group.addButton(self.stop_loss_without_buy_rows_radio)

        stop_loss_mode_radio_layout.addWidget(self.stop_loss_with_buy_rows_radio)
        stop_loss_mode_radio_layout.addWidget(self.stop_loss_without_buy_rows_radio)
        stop_loss_mode_radio_layout.addStretch()

        stop_loss_mode_layout.addLayout(stop_loss_mode_radio_layout)

        # Stop Loss Percentage Input
        stop_loss_input_layout = QHBoxLayout()

        stop_loss_input_label = QLabel("Stop Loss %:")
        stop_loss_input_label.setStyleSheet("color: white; font-weight: bold; font-size: 10px;")
        stop_loss_input_layout.addWidget(stop_loss_input_label)

        self.stop_loss_input = QLineEdit()
        self.stop_loss_input.setPlaceholderText("Enter percentage (e.g., 5.0)")
        self.stop_loss_input.setStyleSheet(get_text_area_style(True))
        self.stop_loss_input.setMaximumWidth(100)
        self.stop_loss_input.setText("0.0")
        self.stop_loss_input.textChanged.connect(self.on_stop_loss_changed)
        stop_loss_input_layout.addWidget(self.stop_loss_input)

        percent_label = QLabel("%")
        percent_label.setStyleSheet("color: white; font-size: 10px;")
        stop_loss_input_layout.addWidget(percent_label)

        stop_loss_input_layout.addStretch()

        stop_loss_mode_layout.addLayout(stop_loss_input_layout)

        self.stop_loss_mode_frame.setVisible(False)  # Caché par défaut
        stop_loss_main_layout.addWidget(self.stop_loss_mode_frame)

        profit_mode_layout.addLayout(stop_loss_main_layout)

        # Connecter les signaux du stop loss (après création des widgets)
        self.stop_loss_enabled_radio.toggled.connect(self.on_stop_loss_enabled_changed)
        self.stop_loss_with_buy_rows_radio.toggled.connect(self.on_stop_loss_mode_changed)
        self.stop_loss_without_buy_rows_radio.toggled.connect(self.on_stop_loss_mode_changed)

        scroll_layout.addWidget(profit_mode_frame, row, 0, 1, 2)
        row += 1

        # Initialiser l'état correct des éléments selon le mode par défaut
        self.on_profit_mode_changed()
        self.on_stop_loss_enabled_changed()





        button_frame = QFrame()
        button_frame.setStyleSheet(get_frame_style(True))
        button_layout = QHBoxLayout(button_frame)
        self.start_button = QPushButton("Start Trading")
        self.start_button.setObjectName("startTrading")
        self.start_button.setProperty("class", "standardButton")
        self.start_button.setStyleSheet(get_standard_button_style(True))
        self.start_button.clicked.connect(self.check_and_start_trading)
        self.start_button.clicked.connect(self.nano_processor.start_watching)
        self.stop_button = QPushButton("Stop Trading")
        self.stop_button.setObjectName("stopTrading")
        self.stop_button.setProperty("class", "standardButton")
        self.stop_button.setStyleSheet(get_standard_button_style(True))
        self.stop_button.clicked.connect(self.stop_nano_trading)
        # Connecter le bouton Stop Trading à la méthode stop_watching du PriceWatcher
        self.stop_button.clicked.connect(self.nano_processor.stop_watching)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        scroll_layout.addWidget(button_frame, row, 0, 1, 2)

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
        self.update_button_states()

        return widget





    def save_configuration_to_file(self):
        try:
            # Vérifier que l'application Qt est complètement initialisée
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                self.display_to_status_bar("Error", "Application not fully initialized. Please wait a moment and try again.")
                return

            # Vérifier que tous les widgets nécessaires sont initialisés
            if not hasattr(self, 'nano_trading_exchange_vars') or not self.nano_trading_exchange_vars:
                self.display_to_status_bar("Error", "Configuration interface not ready. Please wait for complete initialization.")
                return

            options = QFileDialog.Options()
            file_name, _ = QFileDialog.getSaveFileName(None, "Save Configuration", "", "Nano Trading Config (*.nta)", options=options)

            if file_name:
                if not file_name.endswith('.nta'):
                    file_name += '.nta'

                # Vérifier que toutes les conditions sont remplies avant de sauvegarder
                if not self.are_all_conditions_fulfilled():
                    self.display_to_status_bar("Error", "Please fulfill all conditions before saving.")
                    return

                # Récupérer toutes les valeurs de la fenêtre de configuration avec protection
                try:
                    config_data = {
                        "exchanges": self._safe_get_checked_exchanges(),
                        "trade_amount": self._safe_get_text(self.trade_amount_entry),
                        "num_trades": self._safe_get_text(self.num_trades_entry),
                        "symbols": {
                            "USDT": self._safe_is_checked(self.usdt_checkbox),
                            "USDC": self._safe_is_checked(self.usdc_checkbox),
                            "BTC": self._safe_is_checked(self.btc_checkbox),
                            "custom_symbol": self._safe_get_text(self.custom_symbol_entry)
                        },
                        "excluded_tokens": self._safe_get_text(self.excluded_tokens_entry),
                        "excluded_end_prefix": self._safe_get_text(self.excluded_end_prefix_entry),
                        "excluded_start_prefix": self._safe_get_text(self.excluded_start_prefix_entry),
                        "search_index_range": {
                            "start": self._safe_get_text(self.search_index_start_entry),
                            "end": self._safe_get_text(self.search_index_end_entry)
                        },
                        "timeframe": self._safe_get_current_text(self.timeframe_combobox),
                        "threshold": self._safe_get_text(self.threshold_entry),
                        "liquidity_filtering": {
                            "min_volume_usdt": self._safe_get_text(self.min_volume_entry) or str(self.min_volume_usdt),
                            "max_spread_pct": self._safe_get_text(self.max_spread_entry) or str(self.max_spread_pct)
                        },
                        "main_action": self._safe_get_current_text(self.main_action_combobox),
                        "timeframes_depth": self._safe_get_checkboxes_dict(self.timeframe_checkboxes),
                        "indicators": self._safe_get_checkboxes_dict(self.indicator_vars),
                        # Sérialiser les configurations d'achat/vente avec protection
                        "buy_setup": self._safe_serialize_rows(self.buy_rows, 'buy'),
                        "sell_setup": self._safe_serialize_rows(self.sell_rows, 'sell'),
                        # Sauvegarder les actions des timeframes de profondeur avec protection
                        "timeframes_depth_actions": self._safe_get_depth_actions(),
                        # Nouveaux modes de profit
                        "profit_calculation_mode": self.get_profit_calculation_mode(),
                        "fixed_profit_amount": self._safe_get_text(self.fixed_profit_entry) if hasattr(self, 'fixed_profit_entry') else "0.15",
                        # Paramètres de stop loss
                        "stop_loss_enabled": self._safe_is_checked(self.stop_loss_enabled_radio) if hasattr(self, 'stop_loss_enabled_radio') else False,
                        "stop_loss_mode": self.get_stop_loss_mode() if self.is_stop_loss_enabled() else "with_buy_rows",
                        "stop_loss_percentage": self._safe_get_text(self.stop_loss_input) if hasattr(self, 'stop_loss_input') else "0.0"
                    }
                except RuntimeError as e:
                    if "internal C++ object" in str(e):
                        self.display_to_status_bar("Error", "Configuration widgets not ready. Please wait for complete initialization and try again.")
                        return
                    else:
                        raise e

                # Sauvegarder sous format JSON dans un fichier .nta
                with open(file_name, 'w') as f:
                    json.dump(config_data, f, indent=4)

                self.display_to_status_bar("Configuration saved", f"Configuration saved to {file_name}")

        except Exception as e:
            self.display_to_status_bar("Error", f"An error occurred while saving: {str(e)}")

    # Méthodes helper sécurisées pour éviter les erreurs "internal C++ object"
    def _safe_get_text(self, widget):
        """Récupère le texte d'un widget de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'text'):
                text = widget.text()
                return text if text else ""
        except RuntimeError:
            pass
        return ""

    def _safe_get_current_text(self, widget):
        """Récupère le texte actuel d'un combobox de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'currentText'):
                return widget.currentText()
        except RuntimeError:
            pass
        return ""

    def _safe_is_checked(self, widget):
        """Vérifie si un checkbox est coché de manière sécurisée."""
        try:
            if widget and hasattr(widget, 'isChecked'):
                return widget.isChecked()
        except RuntimeError:
            pass
        return False

    def _safe_get_checked_exchanges(self):
        """Récupère les exchanges cochés de manière sécurisée."""
        checked_exchanges = []
        try:
            if hasattr(self, 'nano_trading_exchange_vars'):
                for ex, checkbox in self.nano_trading_exchange_vars.items():
                    if self._safe_is_checked(checkbox):
                        checked_exchanges.append(ex)
        except RuntimeError:
            pass
        return checked_exchanges

    def _safe_get_checkboxes_dict(self, checkboxes_dict):
        """Récupère un dictionnaire de checkboxes de manière sécurisée."""
        result = {}
        try:
            if checkboxes_dict:
                for key, checkbox in checkboxes_dict.items():
                    result[key] = self._safe_is_checked(checkbox)
        except RuntimeError:
            pass
        return result

    def _safe_serialize_rows(self, rows, row_type):
        """Sérialise les lignes buy/sell de manière sécurisée."""
        serialized_rows = []
        try:
            if rows:
                for row in rows:
                    if row_type == 'buy':
                        serialized_row = {
                            "percentage_combobox": self._safe_get_current_text(row.get("percentage_combobox")),
                            "amount_entry": self._safe_get_text(row.get("amount_entry")),
                            "price_to_buy": row.get("price_to_buy", 0.0)
                        }
                    else:  # sell
                        serialized_row = {
                            "percentage_combobox": self._safe_get_current_text(row.get("percentage_combobox")),
                            "sell_percentage_entry": self._safe_get_text(row.get("sell_percentage_entry")),
                            "price_to_sell": row.get("price_to_sell", 0.0)
                        }
                    serialized_rows.append(serialized_row)
        except RuntimeError:
            pass
        return serialized_rows

    def _safe_get_depth_actions(self):
        """Récupère les actions de profondeur de manière sécurisée."""
        depth_actions = {}
        try:
            if hasattr(self, 'depth_action_comboboxes') and self.depth_action_comboboxes:
                for timeframe, combobox in self.depth_action_comboboxes.items():
                    depth_actions[timeframe] = self._safe_get_current_text(combobox)
        except RuntimeError:
            pass
        return depth_actions


    def open_configuration_file(self):
        try:
            options = QFileDialog.Options()
            file_name, _ = QFileDialog.getOpenFileName(None, "Open Configuration", "", "Nano Trading Config (*.nta)", options=options)

            if file_name:
                # Charger le fichier de configuration
                with open(file_name, 'r') as f:
                    config_data = json.load(f)

                # Vérifier que toutes les conditions nécessaires sont présentes dans le fichier
                if not self.is_configuration_valid(config_data):
                    self.display_to_status_bar("Error", "Invalid configuration file. Please ensure all required data is present.")
                    return

                # Restaurer les valeurs dans les widgets
                for exchange, checkbox in self.nano_trading_exchange_vars.items():
                    checkbox.setChecked(exchange in config_data["exchanges"])

                self.trade_amount_entry.setText(config_data["trade_amount"])
                self.num_trades_entry.setText(config_data["num_trades"])

                self.usdt_checkbox.setChecked(config_data["symbols"].get("USDT", False))
                self.usdc_checkbox.setChecked(config_data["symbols"].get("USDC", False))
                self.btc_checkbox.setChecked(config_data["symbols"].get("BTC", False))
                self.custom_symbol_entry.setText(config_data["symbols"].get("custom_symbol", ""))

                self.excluded_tokens_entry.setText(config_data["excluded_tokens"])
                self.excluded_end_prefix_entry.setText(config_data["excluded_end_prefix"])
                self.excluded_start_prefix_entry.setText(config_data["excluded_start_prefix"])

                self.search_index_start_entry.setText(config_data["search_index_range"]["start"])
                self.search_index_end_entry.setText(config_data["search_index_range"]["end"])

                self.timeframe_combobox.setCurrentText(config_data["timeframe"])
                self.threshold_entry.setText(config_data["threshold"])

                # Charger les paramètres de filtrage de liquidité
                if "liquidity_filtering" in config_data:
                    min_volume_value = config_data["liquidity_filtering"].get("min_volume_usdt", str(self.min_volume_usdt))
                    max_spread_value = config_data["liquidity_filtering"].get("max_spread_pct", str(self.max_spread_pct))
                    self.min_volume_entry.setText(min_volume_value)
                    self.max_spread_entry.setText(max_spread_value)
                else:
                    # Utiliser les valeurs par défaut si la section n'existe pas
                    self.min_volume_entry.setText(str(self.min_volume_usdt))
                    self.max_spread_entry.setText(str(self.max_spread_pct))

                self.main_action_combobox.setCurrentText(config_data["main_action"])

                for tf, checkbox in self.timeframe_checkboxes.items():
                    checkbox.setChecked(config_data["timeframes_depth"].get(tf, False))

                # Mettre à jour les comboboxes des actions des timeframes de profondeur
                self.update_timeframe_actions()

                # Restaurer les actions pour les timeframes de profondeur
                for timeframe, action in config_data.get("timeframes_depth_actions", {}).items():
                    if timeframe in self.depth_action_comboboxes:
                        combobox = self.depth_action_comboboxes[timeframe]
                        combobox.setCurrentText(action)

                for ind, checkbox in self.indicator_vars.items():
                    checkbox.setChecked(config_data["indicators"].get(ind, False))

                # Restaurer les conditions de buy et sell
                self.restore_buy_conditions(config_data["buy_setup"])
                self.restore_sell_conditions(config_data["sell_setup"])

                # Restaurer les nouveaux modes de profit
                if "profit_calculation_mode" in config_data:
                    self.set_profit_calculation_mode(config_data["profit_calculation_mode"])
                
                if "fixed_profit_amount" in config_data:
                    self.set_fixed_profit_amount(config_data["fixed_profit_amount"])

                # Restaurer les paramètres de stop loss
                if "stop_loss_enabled" in config_data:
                    self.set_stop_loss_enabled(config_data["stop_loss_enabled"])
                
                if "stop_loss_mode" in config_data:
                    self.set_stop_loss_mode(config_data["stop_loss_mode"])
                
                if "stop_loss_percentage" in config_data:
                    self.set_stop_loss_percentage(config_data["stop_loss_percentage"])

                # Vérifier que les boutons "Start Trading" et "Stop Trading" sont bien visibles
                self.update_button_states()
                self.display_to_status_bar("Configuration loaded", f"Configuration loaded from {file_name}")

        except Exception as e:
            self.display_to_status_bar("Error", f"An error occurred while opening: {str(e)}")


    def are_all_conditions_fulfilled(self):
        # Vérifier que les champs obligatoires sont remplis
        basic_conditions = (
            bool(self.trade_amount_entry.text()) and
            bool(self.num_trades_entry.text()) and
            bool(self.timeframe_combobox.currentText())
        )

        # Vérifier que les widgets de liquidité existent et sont initialisés
        # Ces champs peuvent être vides (utiliseront les valeurs par défaut)
        liquidity_widgets_ready = (
            hasattr(self, 'min_volume_entry') and self.min_volume_entry is not None and
            hasattr(self, 'max_spread_entry') and self.max_spread_entry is not None
        )

        # Valider les valeurs numériques des champs de liquidité s'ils sont remplis
        liquidity_values_valid = self._validate_liquidity_values()

        return basic_conditions and liquidity_widgets_ready and liquidity_values_valid

    def _validate_liquidity_values(self):
        """Valide les valeurs numériques des champs de liquidité."""
        try:
            # Vérifier min_volume_entry
            min_volume_text = self._safe_get_text(self.min_volume_entry)
            if min_volume_text:
                min_volume = float(min_volume_text)
                if min_volume < 0:
                    self.display_to_status_bar("Error", "Minimum volume must be positive")
                    return False

            # Vérifier max_spread_entry
            max_spread_text = self._safe_get_text(self.max_spread_entry)
            if max_spread_text:
                max_spread = float(max_spread_text)
                if max_spread < 0 or max_spread > 100:
                    self.display_to_status_bar("Error", "Maximum spread must be between 0 and 100%")
                    return False

            return True
        except ValueError:
            self.display_to_status_bar("Error", "Invalid numeric values in liquidity fields")
            return False


    def is_configuration_valid(self, config_data):
        # Implémentez ici la logique pour vérifier que le fichier de configuration contient toutes les informations nécessaires
        required_keys = ["exchanges", "trade_amount", "num_trades", "symbols", "timeframe", "threshold"]
        return all(key in config_data for key in required_keys)




    def restore_buy_conditions(self, buy_conditions):
        """
        Restaure les conditions d'achat à partir du fichier de configuration.
        """
        # Vider les conditions actuelles avant de les restaurer
        self.buy_rows.clear()

        # Supprimer les widgets actuels pour éviter les doublons
        layout = self.buy_setup_layout
        for i in reversed(range(layout.count())):
            widget = layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Ajouter chaque condition de la configuration à l'interface
        for condition in buy_conditions:
            self.add_buy_row(layout)  # Ajouter une nouvelle ligne d'achat
            row = self.buy_rows[-1]  # Obtenir la dernière ligne ajoutée

            # Remplir les widgets avec les données du fichier
            row["percentage_combobox"].setCurrentText(condition["percentage_combobox"])
            row["amount_entry"].setText(condition["amount_entry"])
            row["price_to_buy"] = condition["price_to_buy"]  # Restaurer la valeur du prix



    def restore_sell_conditions(self, sell_conditions):
        """
        Restaure les conditions de vente à partir du fichier de configuration.
        """
        # Vider les conditions actuelles avant de les restaurer
        self.sell_rows.clear()

        # Supprimer les widgets actuels pour éviter les doublons
        layout = self.sell_setup_layout
        for i in reversed(range(layout.count())):
            widget = layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Ajouter chaque condition de la configuration à l'interface
        for condition in sell_conditions:
            self.add_sell_row(layout)  # Ajouter une nouvelle ligne de vente
            row = self.sell_rows[-1]  # Obtenir la dernière ligne ajoutée

            # Remplir les widgets avec les données du fichier
            row["percentage_combobox"].setCurrentText(condition["percentage_combobox"])
            row["sell_percentage_entry"].setText(condition["sell_percentage_entry"])
            row["price_to_sell"] = condition["price_to_sell"]  # Restaurer la valeur du prix




    def update_timeframe_actions(self):
        self.depth_action_comboboxes = {}

        """Met à jour les comboboxes d'action des timeframes en fonction des cases cochées."""
        # Supprimer les anciens widgets du QGridLayout
        while self.timeframe_action_grid.count():
            item = self.timeframe_action_grid.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Réinitialiser le dictionnaire des comboboxes
        self.depth_action_comboboxes.clear()

        # Réinitialiser les colonnes et les lignes pour le placement des nouveaux widgets
        current_row = 0  # Ligne de départ pour les timeframes
        current_col = 0  # Colonne de départ

        # Créer et ajouter les nouveaux labels et comboboxes pour les timeframes sélectionnés
        for timeframe, checkbox in self.timeframe_checkboxes.items():
            if checkbox.isChecked():
                # Créer un label pour chaque timeframe
                label = QLabel(f"{timeframe} Depth Action:")
                self.timeframe_action_grid.addWidget(label, current_row, current_col)  # Ajouter le label dans la colonne actuelle

                # Créer un combobox pour chaque timeframe
                combobox_depth_action = QComboBox()
                combobox_depth_action.setObjectName("customComboBox")  # Définir l'ID personnalisé
                combobox_depth_action.addItems(["Buy", "Sell", "No Action"])
                combobox_depth_action.setMinimumWidth(80)  # Largeur adaptative
                combobox_depth_action.setStyleSheet(get_combobox_style(True))  # Appliquer le style personnalisé

                # Ajouter le combobox à côté du label
                self.timeframe_action_grid.addWidget(combobox_depth_action, current_row + 1, current_col)  # Ajouter le combobox dans la même colonne

                # Stocker le combobox dans le dictionnaire pour usage ultérieur
                self.depth_action_comboboxes[timeframe] = combobox_depth_action

                # Mise à jour des colonnes et lignes
                current_col += 1  # Avancer d'une colonne
                if current_col >= 6:  # Changer de ligne après 6 comboboxes
                    current_col = 0  # Réinitialiser la colonne
                    current_row += 2  # Passer à la prochaine paire de lignes (une pour les labels, une pour les comboboxes)

        # Actualiser le layout pour appliquer les changements
        self.timeframe_action_grid.update()



    def get_selected_timeframe_actions(self):
        selected_timeframe_actions = []

        # Parcourir tous les timeframes dans depth_action_comboboxes
        for timeframe, combobox in self.depth_action_comboboxes.items():
            # Récupérer l'action sélectionnée dans le combobox
            action = combobox.currentText()

            # Créer une entrée sous forme de dictionnaire pour le timeframe et l'action
            selected_timeframe_actions.append({
                "timeframe_depth_action": timeframe,
                "action": action
            })

        return selected_timeframe_actions




    def update_available_timeframes(self):
        selected_exchanges = [exchange for exchange, checkbox in self.nano_trading_exchange_vars.items() if checkbox.isChecked()]

        if not selected_exchanges:
            # Si aucun échange n'est sélectionné, désactiver tous les timeframes
            for checkbox in self.timeframe_checkboxes.values():
                checkbox.setDisabled(True)
            if self.timeframe_combobox:
                self.timeframe_combobox.clear()  # Vider le combobox principal si aucun exchange sélectionné
            return

        # Ensemble pour stocker les timeframes communs entre tous les échanges sélectionnés
        common_timeframes = None

        # Itérer sur chaque échange sélectionné
        for exchange_name in selected_exchanges:
            exchange = getattr(ccxt, exchange_name)({
                'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
            })

            try:
                # Charger les marchés pour obtenir les informations sur les timeframes
                exchange.load_markets()

                # Récupérer les timeframes disponibles pour cet échange
                available_timeframes = set(exchange.timeframes)

                if common_timeframes is None:
                    # Si c'est le premier échange sélectionné, initialiser les timeframes communs
                    common_timeframes = available_timeframes
                else:
                    # Prendre l'intersection avec les timeframes disponibles des autres échanges
                    common_timeframes &= available_timeframes

            except Exception as e:
                self.update_status.emit("Error", f"Error loading markets for {exchange_name}: {e}")
                return

        # Si common_timeframes est toujours None, cela signifie que quelque chose s'est mal passé
        if common_timeframes is None:
            return

        # Définir l'ordre logique des timeframes
        sorted_timeframes_order = [
            "1s", "5s", "15s", "30s", "45s",
            "1m", "3m", "5m", "15m", "30m",
            "1h", "2h", "4h", "6h", "8h", "12h",
            "1d", "3d", "1w", "1M", "3M"
        ]

        # Tri personnalisé en fonction de cet ordre
        sorted_timeframes = [tf for tf in sorted_timeframes_order if tf in common_timeframes]

        if self.timeframe_combobox:
            self.timeframe_combobox.clear()  # Vider le combobox
            if common_timeframes:
                # Ajouter uniquement les timeframes communs triés par ordre croissant
                self.timeframe_combobox.addItems(sorted_timeframes)

                # Si le timeframe actuellement sélectionné n'est pas dans les timeframes communs, sélectionner le premier
                current_timeframe = self.timeframe_combobox.currentText()
                if current_timeframe not in common_timeframes:
                    self.timeframe_combobox.setCurrentIndex(0)  # Sélectionner le premier timeframe disponible

        # Mettre à jour l'état des checkboxes des timeframes de profondeur
        for timeframe, checkbox in self.timeframe_checkboxes.items():
            if timeframe in common_timeframes:
                checkbox.setDisabled(False)
            else:
                checkbox.setDisabled(True)
                checkbox.setChecked(False)  # Désactiver et décocher si indisponible

        # Même logique pour les timeframes depth
        self.update_depth_timeframes(common_timeframes)
        # Appel de la fonction de mise à jour des comboboxes d'action
        self.update_timeframe_actions()


    def update_depth_timeframes(self, common_timeframes):
        self.timeframe_checkboxes_layout.clear()
        """Met à jour les checkboxes des timeframes de profondeur en fonction des timeframes disponibles."""
        # Ne pas vider les checkboxes existantes
        for checkbox in self.timeframe_checkboxes.values():
            checkbox.setDisabled(True)
            checkbox.setChecked(False)

        # Ajouter uniquement les timeframes de profondeur disponibles dans common_timeframes
        col = 0
        row = 0
        for timeframe in sorted(common_timeframes):
            checkbox = self.timeframe_checkboxes.get(timeframe)
            if checkbox:
                checkbox.setDisabled(False)
                self.timeframe_checkboxes_layout.addWidget(checkbox, row, col)
                row += 1
                if row >= 5:
                    row = 0
                    col += 1



    # Fonction pour mettre à jour le texte du label lorsque la sélection change
    def update_tokens_info_label(self):
        selected_exchanges = [ex for ex, checkbox in self.nano_trading_exchange_vars.items() if checkbox.isChecked()]
        selected_currency = self.get_selected_currency()
        tokens_info_text = self.get_tokens_information(selected_exchanges, selected_currency)
        self.tokens_info_label.setText(tokens_info_text)





    def check_exchange_balance(self, exchange_name):
        exchange_class = getattr(ccxt, exchange_name)
        exchange = exchange_class({
            'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
            'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
            'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
        })
        try:
            balance = exchange.fetch_balance()
            for currency, amount in balance['free'].items():
                if currency == 'USDT':
                    self.usdt_checkbox.setEnabled(amount > 0)
                elif currency == 'USDC':
                    self.usdc_checkbox.setEnabled(amount > 0)
                elif currency == 'BTC':
                    self.btc_checkbox.setEnabled(amount > 0)
            self.update_available_timeframes()

        except Exception as e:
            self.display_to_nano_terminal("Error", f"Error fetching balance for {exchange_name}: Maybe you did not select your currency", "info")


    # fonction pour récupérer les informations des tokens et construire le texte
    def get_tokens_information(self,selected_exchanges, selected_currency):
        token_info_list = []
        for exchange_name in selected_exchanges:
            try:
                exchange = getattr(ccxt, exchange_name)({
                    'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                    'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                    'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
                })
                exchange.load_markets()
                tokens = [symbol for symbol in exchange.symbols if symbol.endswith(f'/{selected_currency}')]
                token_info_list.append(f"{exchange_name} has {len(tokens)} tokens quoted with {selected_currency}")
            except Exception as e:
                print(f"Error loading markets for {exchange_name}: {e}")

        # Combiner toutes les informations en une seule chaîne séparée par des virgules
        combined_info = ', '.join(token_info_list)
        return combined_info


    def stop_nano_trading(self):
        # Mettre à jour la barre de statut pour indiquer l'arrêt
        self.update_enhanced_status_bar("🛑 Stopping", "Stopping...", {}, 0, 0, 0.0)

        if self.trading_worker:
            # Déconnecter les signaux du trading_worker
            try:
                self.trading_worker.update_terminal.disconnect(self.display_to_nano_terminal)
            except RuntimeError:
                pass  # Le signal n'était peut-être pas connecté ou déjà déconnecté

            try:
                self.trading_worker.update_status.disconnect(self.display_to_status_bar)
            except RuntimeError:
                pass

            try:
                self.trading_worker.add_trade_signal.disconnect(self.dashboard.exchanges_trades_tables.add_trade_to_table)
            except RuntimeError:
                pass

            try:
                self.trading_worker.update_exchanges_balances_signal.disconnect(self.update_exchanges_balances)
            except RuntimeError:
                pass

            try:
                self.trading_worker.update_statistics_signal.disconnect(
                    self.dashboard.exchange_symbol_trade_data_widget.update_widget_content
                )
            except RuntimeError:
                pass

            self.trading_worker.stop_trading()
            #self.trading_worker = None

        if self.trading_thread:
            self.trading_thread.quit()
            self.trading_thread.wait()
            #self.trading_thread = None

        # Arrêter également le PriceWatcher
        if self.nano_processor and self.nano_processor.worker:
            try:
                self.nano_processor.worker.update_terminal.disconnect(self.display_to_nano_terminal)
            except RuntimeError:
                pass

            try:
                self.nano_processor.worker.trade_updated_signal.disconnect(
                    self.dashboard.exchanges_trades_tables.update_trade_in_ui
                )
            except RuntimeError:
                pass

            try:
                self.db.trade_removed_from_db_signal.disconnect(
                    self.dashboard.exchanges_trades_tables.remove_trade_from_table
                )
            except RuntimeError:
                pass

            try:
                self.nano_processor.worker.graph_removed_signal.disconnect(
                    self.dashboard.exchange_symbol_trade_data_widget.remove_symbol_from_graph
                )
            except RuntimeError:
                pass

            try:
                self.nano_processor.worker.trade_profit_signal.disconnect(
                    self.dashboard.connected_exchanges_widget.update_profits
                )
            except RuntimeError:
                pass
            if self.nano_processor:
                self.nano_processor.stop_watching()
                #self.nano_processor = None



            # Demander à l'utilisateur s'il souhaite supprimer la base de données
        # Demander à l'utilisateur s'il souhaite supprimer la base de données

        if os.path.exists("nano_trading_bot.db"):
            reply = QMessageBox.question(
                None,  # Utiliser 'None' si 'self' n'est pas un QWidget
                'Delete database',
                'Do you want to delete the database?<br><span style="font-size:8px;">If yes, you will have to restart the program if you want to continue.</span>',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.delete_database()
                self.display_to_nano_terminal("Info", "The database has been deleted.", "info")
            else:
                self.display_to_nano_terminal("Info", "The database has been preserved.", "info")

        self.nano_trading_active = False
        self.update_button_states()

        # Stop the bot timer
        if hasattr(self, 'main_window') and self.main_window:
            self.main_window.stop_bot_timer()





    def delete_database(self):
        """Supprimer le fichier de la base de données."""
        db_path = "nano_trading_bot.db"  # Remplacez par le chemin de votre base de données
        if os.path.exists(db_path):
            try:
                os.remove(db_path)
                print(f"Database file {db_path} has been deleted.")
            except Exception as e:
                print(f"Failed to delete the database file {db_path}: {e}")
        else:
            print(f"Database file {db_path} not found.")













    def update_button_states(self):
        """Met à jour l'état des boutons 'Start Trading' et 'Stop Trading'."""
        if self.nano_trading_active:
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
        else:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

    def get_profit_calculation_mode(self):
        """Retourne le mode de calcul de profit sélectionné."""
        if self.average_buy_price_radio.isChecked():
            return "average_buy_price"
        elif self.last_buy_price_radio.isChecked():
            return "last_buy_price"
        elif self.fixed_profit_radio.isChecked():
            return "fixed_profit"
        return "average_buy_price"  # Par défaut

    def set_profit_calculation_mode(self, mode):
        """Définit le mode de calcul de profit."""
        if mode == "average_buy_price":
            self.average_buy_price_radio.setChecked(True)
        elif mode == "last_buy_price":
            self.last_buy_price_radio.setChecked(True)
        elif mode == "fixed_profit":
            self.fixed_profit_radio.setChecked(True)
        else:
            self.average_buy_price_radio.setChecked(True)  # Par défaut

    def on_profit_mode_changed(self):
        """Appelée quand le mode de profit change."""
        mode = self.get_profit_calculation_mode()
        print(f"Profit calculation mode changed to: {mode}")

        # Gérer l'affichage du champ Fixed Profit Amount
        if mode == "fixed_profit":
            # Mode Fixed Profit : afficher le champ fixed profit, cacher les sell rows
            self.fixed_profit_label.setVisible(True)
            self.fixed_profit_entry.setVisible(True)
            self.fixed_profit_usdt_label.setVisible(True)
            self.hide_sell_rows()
        else:
            # Modes Average Buy Price ou Last Buy Price : cacher le champ fixed profit, afficher les sell rows
            self.fixed_profit_label.setVisible(False)
            self.fixed_profit_entry.setVisible(False)
            self.fixed_profit_usdt_label.setVisible(False)
            self.show_sell_rows()

    def hide_sell_rows(self):
        """Cache toutes les lignes de sell setup."""
        if hasattr(self, 'sell_setup_layout'):
            for i in range(self.sell_setup_layout.count()):
                item = self.sell_setup_layout.itemAt(i)
                if item and item.widget():
                    item.widget().setVisible(False)

    def show_sell_rows(self):
        """Affiche toutes les lignes de sell setup."""
        if hasattr(self, 'sell_setup_layout'):
            for i in range(self.sell_setup_layout.count()):
                item = self.sell_setup_layout.itemAt(i)
                if item and item.widget():
                    item.widget().setVisible(True)

    def get_stop_loss_percentage(self):
        """Retourne le pourcentage de stop loss saisi."""
        try:
            return float(self.stop_loss_input.text())  # Retourne la valeur telle quelle (peut être négative)
        except ValueError:
            return 0.0  # Valeur par défaut si l'entrée n'est pas valide

    def set_stop_loss_percentage(self, percentage):
        """Définit le pourcentage de stop loss."""
        try:
            value = float(percentage)  # Accepte les valeurs négatives
            self.stop_loss_input.setText(str(value))
        except (ValueError, TypeError):
            self.stop_loss_input.setText("0.0")

    def is_stop_loss_enabled(self):
        """Retourne True si le stop loss est activé."""
        return hasattr(self, 'stop_loss_enabled_radio') and self.stop_loss_enabled_radio.isChecked()

    def get_stop_loss_mode(self):
        """Retourne le mode stop loss: 'with_buy_rows' ou 'without_buy_rows'."""
        if not self.is_stop_loss_enabled():
            return None

        if hasattr(self, 'stop_loss_with_buy_rows_radio') and self.stop_loss_with_buy_rows_radio.isChecked():
            return 'with_buy_rows'
        elif hasattr(self, 'stop_loss_without_buy_rows_radio') and self.stop_loss_without_buy_rows_radio.isChecked():
            return 'without_buy_rows'
        return 'with_buy_rows'  # Par défaut

    def set_stop_loss_enabled(self, enabled):
        """Active ou désactive le stop loss."""
        if hasattr(self, 'stop_loss_enabled_radio'):
            self.stop_loss_enabled_radio.setChecked(enabled)
            self.on_stop_loss_enabled_changed()

    def set_stop_loss_mode(self, mode):
        """Définit le mode stop loss."""
        if mode == 'with_buy_rows' and hasattr(self, 'stop_loss_with_buy_rows_radio'):
            self.stop_loss_with_buy_rows_radio.setChecked(True)
        elif mode == 'without_buy_rows' and hasattr(self, 'stop_loss_without_buy_rows_radio'):
            self.stop_loss_without_buy_rows_radio.setChecked(True)

        self.on_stop_loss_mode_changed()

    def on_stop_loss_changed(self):
        """Appelée quand la valeur du stop loss change - Validation en temps réel."""
        percentage = self.get_stop_loss_percentage()
        print(f"Stop loss percentage changed to: {percentage}%")

        # Validation intelligente en temps réel
        if (hasattr(self, 'stop_loss_enabled_radio') and
            self.stop_loss_enabled_radio.isChecked() and
            hasattr(self, 'stop_loss_with_buy_rows_radio') and
            self.stop_loss_with_buy_rows_radio.isChecked()):

            # Validation immédiate avec feedback visuel
            is_valid = self.validate_stop_loss_percentage_realtime()
            self.update_stop_loss_input_style(is_valid)

            if not is_valid:
                # Afficher un message d'erreur dans la status bar si disponible
                self.display_stop_loss_validation_error(percentage)

    def get_fixed_profit_amount(self):
        """Retourne le montant de profit fixe saisi."""
        try:
            value = float(self.fixed_profit_entry.text())
            return max(0.0, value)  # S'assurer que la valeur n'est pas négative
        except (ValueError, AttributeError):
            return 0.15  # Valeur par défaut

    def set_fixed_profit_amount(self, amount):
        """Définit le montant de profit fixe."""
        try:
            value = max(0.0, float(amount))  # S'assurer que la valeur n'est pas négative
            self.fixed_profit_entry.setText(str(value))
        except (ValueError, TypeError, AttributeError):
            self.fixed_profit_entry.setText("0.15")

    def on_fixed_profit_changed(self):
        """Appelée quand la valeur du profit fixe change."""
        amount = self.get_fixed_profit_amount()
        print(f"Fixed profit amount changed to: {amount} USDT")
        
    def update_currency_label(self):
        """
        Met à jour le libellé de la devise en fonction de la sélection de l'utilisateur.
        """
        selected_currency = self.get_selected_currency()
        if selected_currency:
            self.currency_label.setText(selected_currency)
            # S'assurer que le label est visible si le mode Fixed Profit est sélectionné
            if hasattr(self, 'fixed_profit_radio') and self.fixed_profit_radio.isChecked():
                self.currency_label.setVisible(True)

    def on_stop_loss_enabled_changed(self):
        """Appelée quand le stop loss est activé/désactivé."""
        is_enabled = self.stop_loss_enabled_radio.isChecked()
        self.stop_loss_mode_frame.setVisible(is_enabled)

        if is_enabled:
            print("Stop Loss enabled")
            # Appliquer la logique selon le mode sélectionné
            self.on_stop_loss_mode_changed()
        else:
            print("Stop Loss disabled")
            # Réafficher les buy rows si elles étaient cachées
            self.show_buy_rows()

    def on_stop_loss_mode_changed(self):
        """Appelée quand le mode stop loss change (avec/sans buy rows)."""
        if not self.stop_loss_enabled_radio.isChecked():
            return

        if self.stop_loss_with_buy_rows_radio.isChecked():
            print("Stop Loss mode: WITH buy rows")
            self.show_buy_rows()
            # Valider que le % stop loss < % dernier buy row
            self.validate_stop_loss_percentage()
        else:
            print("Stop Loss mode: WITHOUT buy rows - will sell total amount")
            self.hide_buy_rows()

    def hide_buy_rows(self):
        """Cache toutes les lignes de buy setup."""
        if hasattr(self, 'buy_setup_layout'):
            for i in range(self.buy_setup_layout.count()):
                item = self.buy_setup_layout.itemAt(i)
                if item and item.widget():
                    item.widget().setVisible(False)

    def show_buy_rows(self):
        """Affiche toutes les lignes de buy setup."""
        if hasattr(self, 'buy_setup_layout'):
            for i in range(self.buy_setup_layout.count()):
                item = self.buy_setup_layout.itemAt(i)
                if item and item.widget():
                    item.widget().setVisible(True)

    def validate_stop_loss_percentage(self):
        """Valide que le % stop loss est inférieur au % du dernier buy row."""
        if not hasattr(self, 'buy_rows') or not self.buy_rows:
            print("ℹ️ No buy rows configured - validation skipped")
            return True

        try:
            stop_loss_pct = self.get_stop_loss_percentage()

            # Si le stop loss est à 0, la validation est automatiquement réussie
            if stop_loss_pct == 0:
                print("ℹ️ Stop loss is 0% - no validation needed")
                return True

            # Initialiser avec None pour détecter la première valeur valide
            max_buy_pct = None
            buy_percentages = []

            # Trouver le pourcentage le plus élevé dans les buy rows
            for row in self.buy_rows:
                if 'percentage_combobox' in row:
                    pct_text = row['percentage_combobox'].currentText().replace('%', '').strip()
                    try:
                        pct_value = float(pct_text)
                        buy_percentages.append(pct_value)
                        # Garder la valeur maximale
                        if max_buy_pct is None or pct_value > max_buy_pct:
                            max_buy_pct = pct_value
                    except ValueError as ve:
                        print(f"⚠️ Warning: Could not convert '{pct_text}' to float: {ve}")
                        continue

            if max_buy_pct is None:
                print("ℹ️ No valid buy row percentages found - validation skipped")
                return True

            # Afficher les valeurs pour le débogage
            print(f"🔍 Validation - Stop Loss: {stop_loss_pct}%, Buy Rows: {buy_percentages}, Max Buy %: {max_buy_pct}")

            # Pour un stop loss valide, il doit être supérieur au pourcentage maximum des buy rows
            # Exemple: Si buy rows = [1.5%, 3%, 4.5%], le stop loss doit être > 4.5%
            if stop_loss_pct <= max_buy_pct:
                print(f"❌ INVALID: Stop loss {stop_loss_pct}% must be > {max_buy_pct}% (highest buy row)")
                print(f"   Buy rows: {[f'{p}%' for p in sorted(buy_percentages)]}")
                return False
            else:
                print(f"✅ VALID: Stop loss {stop_loss_pct}% > {max_buy_pct}% (highest buy row)")
                return True

        except Exception as e:
            print(f"❌ Error validating stop loss: {e}")
            import traceback
            traceback.print_exc()
            return False

    def validate_stop_loss_percentage_realtime(self):
        """Validation en temps réel avec plus de détails."""
        if not hasattr(self, 'buy_rows') or not self.buy_rows:
            print("ℹ️ No buy rows configured yet - stop loss validation skipped")
            return True

        try:
            stop_loss_pct = self.get_stop_loss_percentage()

            # Si le stop loss est à 0, la validation est automatiquement réussie
            if stop_loss_pct == 0:
                print("ℹ️ Stop loss is 0% - no validation needed")
                return True

            # Initialiser avec None pour détecter la première valeur valide
            max_buy_pct = None
            buy_percentages = []

            # Trouver le pourcentage le plus élevé dans les buy rows
            for row in self.buy_rows:
                if 'percentage_combobox' in row:
                    pct_text = row['percentage_combobox'].currentText().replace('%', '').strip()
                    try:
                        pct_value = float(pct_text)
                        buy_percentages.append(pct_value)
                        # Garder la valeur maximale
                        if max_buy_pct is None or pct_value > max_buy_pct:
                            max_buy_pct = pct_value
                    except ValueError as ve:
                        print(f"⚠️ Warning: Could not convert '{pct_text}' to float: {ve}")
                        continue

            if max_buy_pct is None:
                print("ℹ️ No valid buy row percentages found - validation skipped")
                return True

            # Afficher les valeurs pour le débogage
            print(f"🔍 Realtime Validation - Stop Loss: {stop_loss_pct}%, Buy Rows: {buy_percentages}, Max Buy %: {max_buy_pct}")

            # Pour un stop loss valide, il doit être supérieur au pourcentage maximum des buy rows
            is_valid = stop_loss_pct > max_buy_pct
            
            # Mettre à jour le style de l'input en fonction de la validité
            self.update_stop_loss_input_style(is_valid)
            
            if not is_valid:
                print(f"❌ INVALID: Stop loss {stop_loss_pct}% must be > {max_buy_pct}% (highest buy row)")
                print(f"   Buy rows: {[f'{p}%' for p in sorted(buy_percentages)]}")
                self.display_stop_loss_validation_error(stop_loss_pct)
            else:
                print(f"✅ VALID: Stop loss {stop_loss_pct}% > {max_buy_pct}% (highest buy row)")
                
            return is_valid

        except Exception as e:
            print(f"❌ Error in realtime validation: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_stop_loss_input_style(self, is_valid):
        """Met à jour le style de l'input stop loss selon la validité."""
        if not hasattr(self, 'stop_loss_input'):
            return

        if is_valid:
            # Style normal (valide)
            self.stop_loss_input.setStyleSheet(get_text_area_style(True))
        else:
            # Style d'erreur (rouge)
            error_style = get_text_area_style(True) + """
                border: 2px solid #ff4444;
                background-color: rgba(255, 68, 68, 0.1);
            """
            self.stop_loss_input.setStyleSheet(error_style)

    def display_stop_loss_validation_error(self, percentage):
        """Affiche un message d'erreur pour la validation du stop loss."""
        try:
            # Trouver le pourcentage max des buy rows
            max_buy_pct = 0.0
            for row in self.buy_rows:
                if 'percentage_combobox' in row:
                    pct_text = row['percentage_combobox'].currentText().replace('%', '')
                    try:
                        pct_value = float(pct_text)
                        max_buy_pct = max(max_buy_pct, pct_value)
                    except ValueError:
                        continue

            error_msg = f"Stop Loss {percentage}% must be < highest Buy Row {max_buy_pct}%"

            # Afficher dans la status bar si disponible
            if hasattr(self, 'status_bar') and self.status_bar:
                self.status_bar.showMessage(f"❌ {error_msg}", 5000)  # 5 secondes

            # Aussi dans la console pour debug
            print(f"🚨 VALIDATION ERROR: {error_msg}")

        except Exception as e:
            print(f"Error displaying validation error: {e}")

    def on_buy_row_percentage_changed(self):
        """Appelée quand un pourcentage de buy row change - Revalide le stop loss."""
        print("🔄 Buy row percentage changed - revalidating stop loss...")

        # Si le stop loss est activé et en mode "with buy rows", revalider
        if (hasattr(self, 'stop_loss_enabled_radio') and
            self.stop_loss_enabled_radio.isChecked() and
            hasattr(self, 'stop_loss_with_buy_rows_radio') and
            self.stop_loss_with_buy_rows_radio.isChecked()):

            # Revalider le stop loss avec le nouveau pourcentage de buy row
            is_valid = self.validate_stop_loss_percentage_realtime()
            self.update_stop_loss_input_style(is_valid)

            if not is_valid:
                percentage = self.get_stop_loss_percentage()
                self.display_stop_loss_validation_error(percentage)

    def remove_buy_row(self, layout, frame, combobox):
        """Supprime une buy row et revalide le stop loss."""
        try:
            # Supprimer de la liste buy_rows
            self.buy_rows = [row for row in self.buy_rows if row.get('percentage_combobox') != combobox]

            # Supprimer le widget de l'interface
            layout.removeWidget(frame)
            frame.deleteLater()

            print(f"🗑️ Buy row removed - {len(self.buy_rows)} rows remaining")

            # Revalider le stop loss après suppression
            if (hasattr(self, 'stop_loss_enabled_radio') and
                self.stop_loss_enabled_radio.isChecked() and
                hasattr(self, 'stop_loss_with_buy_rows_radio') and
                self.stop_loss_with_buy_rows_radio.isChecked()):

                is_valid = self.validate_stop_loss_percentage_realtime()
                self.update_stop_loss_input_style(is_valid)

                if not is_valid:
                    percentage = self.get_stop_loss_percentage()
                    self.display_stop_loss_validation_error(percentage)

        except Exception as e:
            print(f"Error removing buy row: {e}")


    @Slot()
    def on_trading_finished(self):
        if self.trading_thread:
            self.trading_thread.quit()
            self.trading_thread.wait()

        self.trading_worker = None
        self.trading_thread = None

        self.display_to_nano_terminal("Info", "Trading process has finished.", "info")
        # Mettre à jour la barre de statut pour indiquer l'arrêt complet
        self.update_enhanced_status_bar("✅ Stopped", "Bot Stopped", {}, 0, 0, 0.0)

    def liquidate_all_trades(self, active_trades):
        """Liquide immédiatement tous les trades actifs"""
        liquidated_count = 0

        try:
            for trade in active_trades:
                try:
                    # Get trade details
                    exchange_name = trade.get('exchange', '').lower()
                    symbol = trade.get('symbol', '')
                    base_token = trade.get('base_token', '')
                    amount_to_trade = float(trade.get('amount_to_trade', 0))

                    if not exchange_name or not symbol or amount_to_trade <= 0:
                        print(f"Skipping invalid trade: {trade}")
                        continue

                    # Get exchange instance
                    exchange = None
                    for exch in self.nano_trading_selected_exchanges:
                        if exch.lower() == exchange_name:
                            exchange = self.get_exchange_instance(exch)
                            break

                    if not exchange:
                        print(f"Exchange {exchange_name} not found or not configured")
                        continue

                    # Get current market price
                    ticker = exchange.fetch_ticker(symbol)
                    current_price = ticker['last']

                    # Create market sell order
                    order = exchange.create_market_sell_order(symbol, amount_to_trade)

                    if order:
                        # Calculate profit/loss
                        buy_price = float(trade.get('buy_price', current_price))
                        sell_revenue = amount_to_trade * current_price
                        buy_cost = amount_to_trade * buy_price
                        profit = sell_revenue - buy_cost

                        # Update trade in database as completed
                        self.db.update_trade_status(
                            trade['id'],
                            'completed',
                            current_price,
                            profit,
                            'immediate_liquidation'
                        )

                        liquidated_count += 1
                        print(f"Liquidated {symbol}: {amount_to_trade} at {current_price} (Profit: {profit:.4f})")

                        # Update terminal
                        self.display_to_nano_terminal(
                            "Liquidation",
                            f"🔥 Liquidated {symbol}: {amount_to_trade:.6f} {base_token} at ${current_price:.4f}",
                            "warning"
                        )

                except Exception as trade_error:
                    print(f"Error liquidating trade {trade.get('id', 'unknown')}: {trade_error}")
                    self.display_to_nano_terminal(
                        "Liquidation Error",
                        f"❌ Failed to liquidate {trade.get('symbol', 'unknown')}: {str(trade_error)}",
                        "error"
                    )
                    continue

            # Final summary
            self.display_to_nano_terminal(
                "Liquidation Complete",
                f"✅ Successfully liquidated {liquidated_count} trades",
                "info"
            )

            return liquidated_count

        except Exception as e:
            print(f"Critical error during liquidation: {e}")
            self.display_to_nano_terminal(
                "Liquidation Error",
                f"❌ Critical error during liquidation: {str(e)}",
                "error"
            )
            raise e

    def on_application_exit(self):
        self.stop_nano_trading()
        if self.nano_processor:
            self.nano_processor.stop_watching()
        #self.nano_processor.stop_all_threads()  # Arrêter tous les threads du NanoProcessor


    def add_buy_row(self, layout):
        frame = QFrame()
        frame.setStyleSheet(get_frame_style(True))
        row_layout = QHBoxLayout(frame)

        label = QLabel("If the price falls to:")
        row_layout.addWidget(label)

        combobox = QComboBox()
        combobox.setObjectName("customComboBox")
        combobox.addItems(["1.5%","3%",  "4.5%", "5.4%", "8.1%", "11.7%", "15.3%", "18.1%", "21.6%", "24.3%", "27%", "30.6%", "33.3%", "36%", "39.6%", "42.3%", "45%", "48.6%", "51.3%", "54%", "57.6%", "60.3%", "63%", "66.6%", "69.3%", "72%", "75.6%", "78.3%", "81%", "84.6%", "87.3%", "90%", "93.6%", "96.3%", "100%"])
        combobox.setStyleSheet(get_combobox_style(True))
        # Connecter le signal pour revalider le stop loss quand le buy row change
        combobox.currentTextChanged.connect(self.on_buy_row_percentage_changed)
        row_layout.addWidget(combobox)

        label_amount = QLabel("Token Amount:")
        row_layout.addWidget(label_amount)

        amount_entry = QLineEdit()
        amount_entry.setPlaceholderText("Enter token amount")
        amount_entry.setObjectName("customLineEdit")
        amount_entry.setStyleSheet(get_text_area_style(True))
        row_layout.addWidget(amount_entry)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(2)

        add_button = QPushButton()
        plus_icon_path = recolor_icon("icons/plus.png", (130, 110, 140), "addBuyRowButton-dark.png")
        add_button.setIcon(QIcon(plus_icon_path))
        add_button.setObjectName("addBuyRowButton")
        add_button.setToolTip("Add Buy Order Row")
        add_button.clicked.connect(lambda: self.add_buy_row(layout))
        button_layout.addWidget(add_button)

        remove_button = QPushButton()
        minus_icon_path = recolor_icon("icons/minus.png", (130, 110, 140), "removeBuyRowButton-dark.png")
        remove_button.setIcon(QIcon(minus_icon_path))
        remove_button.setObjectName("removeBuyRowButton")
        remove_button.setToolTip("Remove Buy Order Row")
        remove_button.clicked.connect(lambda: self.remove_buy_row(layout, frame, combobox))
        button_layout.addWidget(remove_button)

        row_layout.addLayout(button_layout)
        layout.addWidget(frame)
        # Ajouter une nouvelle ligne d'achat
        self.buy_rows.append({
            "percentage_combobox": combobox,  # Ceci est un QComboBox
            "amount_entry": amount_entry,     # Ceci est un QLineEdit
            "price_to_buy": 0.0               # Ceci est un float pour stocker le prix
        })






        self.update_remove_button_state(layout, remove_button)
        #self.check_conditions_filled()  # Vérifier que les conditions sont remplies




    def add_sell_row(self, layout):
        frame = QFrame()
        frame.setStyleSheet(get_frame_style(True))
        row_layout = QHBoxLayout(frame)

        label = QLabel("If the price rises to:")
        row_layout.addWidget(label)

        combobox = QComboBox()
        combobox.setObjectName("customComboBox")
        combobox.addItems([ "1.5%", "3%", "4.5%", "6%", "9%", "15%", "24%", "39%", "63%", "102%"])
        combobox.setStyleSheet(get_combobox_style(True))
        row_layout.addWidget(combobox)

        label_percentage = QLabel("Sell Percentage:")
        row_layout.addWidget(label_percentage)

        percentage_entry = QLineEdit()
        percentage_entry.setPlaceholderText("Enter percentage to sell")
        percentage_entry.setObjectName("customLineEdit")
        percentage_entry.setStyleSheet(get_text_area_style(True))
        row_layout.addWidget(percentage_entry)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(2)

        add_button = QPushButton()
        plus_icon_path = recolor_icon("icons/plus.png", (130, 110, 140), "addSellRowButton-dark.png")
        add_button.setIcon(QIcon(plus_icon_path))
        add_button.setObjectName("addSellRowButton")
        add_button.setToolTip("Add Sell Order Row")
        add_button.clicked.connect(lambda: self.add_sell_row(layout))
        button_layout.addWidget(add_button)

        remove_button = QPushButton()
        minus_icon_path = recolor_icon("icons/minus.png", (130, 110, 140), "removeSellRowButton-dark.png")
        remove_button.setIcon(QIcon(minus_icon_path))
        remove_button.setObjectName("removeSellRowButton")
        remove_button.setToolTip("Remove Sell Order Row")
        remove_button.clicked.connect(lambda: layout.removeWidget(frame) or frame.deleteLater())
        button_layout.addWidget(remove_button)

        row_layout.addLayout(button_layout)
        layout.addWidget(frame)
        # Récupérer les valeurs du combobox et du QLineEdit
        percentage_value = combobox.currentText()
        sell_percentage_value = percentage_entry.text()

        # Ajouter une nouvelle ligne de vente
        self.sell_rows.append({
            "percentage_combobox": combobox,      # Ceci est un QComboBox
            "sell_percentage_entry": percentage_entry,  # Ceci est un QLineEdit
            "price_to_sell": 0.0                  # Ceci est un float pour stocker le prix
        })






        self.update_remove_button_state(layout, remove_button)
        #self.check_conditions_filled()  # Vérifier que les conditions sont remplies



    def get_buy_conditions(self, symbol, exchange):
        # Récupérer les conditions d'achat pour un symbole et un échange spécifiques
        return [row for row in self.buy_rows if row.get("symbol") == symbol and row.get("exchange") == exchange]



    def get_sell_conditions(self, symbol, exchange):
        # Récupérer les conditions de vente pour un symbole et un échange spécifiques
        return [row for row in self.sell_rows if row.get("symbol") == symbol and row.get("exchange") == exchange]






    def check_conditions_filled(self):
        # Vérifier qu'au moins une condition d'achat est remplie
        buy_filled = any(
            row.get('amount_entry') and row['amount_entry'].text().strip() and row.get('percentage_combobox').currentText() for row in self.buy_rows
        )

        # Vérifier les conditions de vente en fonction du mode sélectionné
        profit_mode = self.get_profit_calculation_mode()
        if profit_mode == 'fixed_profit':
            # En mode Fixed Profit, on vérifie simplement qu'un montant est défini
            sell_filled = bool(self.get_fixed_profit_amount())
        else:
            # En mode pourcentage, on vérifie les conditions de vente normales
            sell_filled = any(
                row.get('sell_percentage_entry') and row['sell_percentage_entry'].text().strip() and row.get('percentage_combobox').currentText() 
                for row in self.sell_rows
            )

        # Debug: Afficher les entrées actuelles
        print("Conditions d'achat actuelles:")
        for row in self.buy_rows:
            print(f"Montant: {row['amount_entry'].text() if 'amount_entry' in row else 'None'}, Pourcentage: {row['percentage_combobox'].currentText() if 'percentage_combobox' in row else 'None'}")

        print("Conditions de vente actuelles:")
        profit_mode = self.get_profit_calculation_mode()
        for row in self.sell_rows:
            if profit_mode == 'fixed_profit':
                fixed_amount = self.get_fixed_profit_amount()
                currency = self.get_selected_currency()
                print(f"Fixed Profit: {fixed_amount} {currency}")
            else:
                print(f"Pourcentage: {row['sell_percentage_entry'].text() if 'sell_percentage_entry' in row else 'None'}, Pourcentage Vente: {row['percentage_combobox'].currentText() if 'percentage_combobox' in row else 'None'}")

        if not buy_filled or not sell_filled:
            print("You must fulfill at least one purchase condition and one sale condition before starting the bot.")
            # Fenêtre d'avertissement pour l'utilisateur
            QMessageBox.warning(None, "Missing Condition", "You must fulfill at least one purchase condition and one sale condition before starting the bot.")
            return False

        return True










    def show_error_message(self, message):
        """
        Affiche une fenêtre pop-up pour informer l'utilisateur.
        """
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Warning)
        msg.setText(message)
        msg.setWindowTitle("Trading Bot Error")
        msg.exec()



    def update_remove_button_state(self, layout, remove_button):
        if layout.count() > 1:
            remove_button.setEnabled(True)
        else:
            remove_button.setEnabled(False)

    def get_selected_currency(self):
        if self.usdt_checkbox.isChecked():
            return 'USDT'
        elif self.usdc_checkbox.isChecked():
            return 'USDC'
        elif self.btc_checkbox.isChecked():
            return 'BTC'
        else:
            custom_symbol = self.custom_symbol_entry.text().upper()
            if custom_symbol:
                return custom_symbol  # Return the actual custom symbol
        return None






    def on_currency_selected(self):
        """
        Méthode appelée lorsque l'utilisateur sélectionne une devise.
        """
        self.selected_currency = self.get_selected_currency()
        if self.selected_currency:
            # Émettre un signal indiquant que la devise a été sélectionnée
            self.currency_selected_signal.emit()





    def add_unique_trade_pair(self, pair):
        if pair not in self.unique_traded_pairs:
            self.unique_traded_pairs.add(pair)

            return True
        return False



    def remove_unique_trade_pair(self, pair):
        self.unique_traded_pairs.discard(pair)



    def initialize_timeframe_checkboxes(self):
        all_timeframes = [
            "1s", "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M", "3M"
        ]
        self.timeframe_checkboxes = {}
        row, col = 0, 0
        grid_layout = self.timeframe_checkboxes_frame.layout()

        for timeframe in all_timeframes:
            checkbox = QCheckBox(timeframe, parent=self.timeframe_checkboxes_frame)
            grid_layout.addWidget(checkbox, row, col)
            self.timeframe_checkboxes[timeframe] = checkbox

            row += 1
            if row >= 5:
                row = 0
                col += 1

                    # Appel initial pour s'assurer que les timeframes sont configurés au début





    def filter_liquid_symbols(self, exchange, symbols, min_volume_usdt=1000000, max_spread_pct=0.1):
        """
        Filtre les symboles basés sur le volume et le spread pour éviter les shitcoins illiquides.
        """
        liquid_symbols = []

        for symbol in symbols:
            try:
                # Récupérer les données du ticker
                ticker = exchange.fetch_ticker(symbol)
                volume = ticker.get('quoteVolume')  # Volume en quote currency (ex: USDT)

                if volume is None or volume < min_volume_usdt:
                    continue

                # Récupérer le carnet d'ordres pour calculer le spread
                order_book = exchange.fetch_order_book(symbol, limit=5)
                bids = order_book.get('bids', [])
                asks = order_book.get('asks', [])

                if not bids or not asks:
                    continue

                best_bid = bids[0][0]
                best_ask = asks[0][0]
                spread_pct = (best_ask - best_bid) / best_ask * 100

                # Vérifier si le symbole respecte les critères de liquidité
                if spread_pct <= max_spread_pct:
                    liquid_symbols.append({
                        'symbol': symbol,
                        'volume_24h': round(volume, 2),
                        'spread_pct': round(spread_pct, 3)
                    })
                    self.display_to_nano_terminal("Liquidity Check",
                                                f"{symbol}: Volume={volume:,.0f} USDT, Spread={spread_pct:.3f}%",
                                                "info")
                else:
                    self.display_to_nano_terminal("Liquidity Filter",
                                                f"{symbol} rejected: Spread={spread_pct:.3f}% > {max_spread_pct}%",
                                                "warning")

            except Exception as e:
                self.display_to_nano_terminal("Liquidity Error", f"{symbol}: {str(e)}", "error")
                continue

        return [item['symbol'] for item in liquid_symbols]

    def fetch_data(self, exchange, symbol, timeframe):
        try:
            exchange_name = getattr(exchange, 'name', None)
            if exchange_name is None:
                return None

            data = exchange.fetch_ohlcv(symbol, timeframe)
            if data is None or len(data) == 0:
                return None

            df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            return df
        except Exception as e:
            print(f"Error while retrieving data: {e}")
            return None

    def calculate_indicators(self, df):
        for category, indics in indicators.items():
            for indicator, formula in indics.items():
                exec(formula)

    def evaluate_market_and_trade(self, df, selected_indicators, layout, threshold, timeframe, symbol):
        # S'assurer de ne pas dépasser le nombre maximum de trades
        self.num_trades = int(self.num_trades_entry.text())
        if len(self.unique_traded_pairs) >= self.num_trades:
            self.display_to_nano_terminal("Info", "Transaction limit reached. No new market assessment will be carried out.", "info")
            return None, 0  # Ignorer les évaluations supplémentaires
        # Évaluer le marché avec les indicateurs sélectionnés
        action, score = self.evaluate_market(df, selected_indicators, threshold, symbol, timeframe)

        # Retourner l'action et le score pour qu'ils soient traités ailleurs
        return action, score


    def evaluate_market(self, df, selected_indicators, threshold, symbol, timeframe):
        """
        Évalue le marché basé sur les indicateurs sélectionnés et renvoie l'action recommandée (achat, vente ou rien).
        """
        try:
            buy_signals = 0
            sell_signals = 0
            total_indicators = len(selected_indicators)
            indicators_examined = 0

            if df is None or df.empty:
                return None, "Error: Dataframe is empty or None"

            # Calculate all indicators first
            self.calculate_indicators(df)

            # Process each indicator
            for indicator in selected_indicators:
                try:
                    """
                    if indicator == 'SMA':
                        if df['close'].iloc[-1] > df['SMA'].iloc[-1]:
                            buy_signals += 1
                            print('SMA Buy')

                        elif df['close'].iloc[-1] < df['SMA'].iloc[-1]:
                            sell_signals += 1
                            print('SMA Sell')
                    """
                    if indicator == 'SMA':
                        price = df['close'].iloc[-1]
                        sma = df['SMA'].iloc[-1]
                        price_distance = (price - sma) / sma * 100

                        if price_distance > 0.5:  # Prix 0.5% au-dessus SMA
                            buy_signals += 1
                            print(f'SMA Buy - Distance: {price_distance:.2f}%')
                        elif price_distance < -0.5:  # Prix 0.5% en-dessous SMA
                            sell_signals += 1
                            print(f'SMA Sell - Distance: {price_distance:.2f}%')

                    """
                    if indicator == 'EMA':
                        if df['close'].iloc[-1] > df['EMA'].iloc[-1]:
                            buy_signals += 1
                            print('EMA Buy')

                        elif df['close'].iloc[-1] < df['EMA'].iloc[-1]:
                            sell_signals += 1
                            print('EMA Sell')
                    """
                    if indicator == 'EMA':
                        price = df['close'].iloc[-1]
                        ema = df['EMA'].iloc[-1]
                        price_distance = (price - ema) / ema * 100

                        if price_distance > 0.3:  # EMA plus sensible
                            buy_signals += 1
                            print(f'EMA Buy - Distance: {price_distance:.2f}%')
                        elif price_distance < -0.3:
                            sell_signals += 1
                            print(f'EMA Sell - Distance: {price_distance:.2f}%')

                    """
                    if indicator == 'WMA':
                        if df['close'].iloc[-1] > df['WMA'].iloc[-1]:
                            buy_signals += 1
                            print('WMA Buy')
                        elif df['close'].iloc[-1] < df['WMA'].iloc[-1]:
                            sell_signals += 1
                            print('WMA Sell')

                    if indicator == 'WMA':
                        if df['close'].iloc[-1] > df['WMA'].iloc[-1]:
                            buy_signals += 1
                            print('WMA Buy')
                        elif df['close'].iloc[-1] < df['WMA'].iloc[-1]:
                            sell_signals += 1
                            print('WMA Sell')
                    """
                    if indicator == 'WMA':
                        price = df['close'].iloc[-1]
                        wma = df['WMA'].iloc[-1]
                        price_distance = (price - wma) / wma * 100

                        if price_distance > 0.4:
                            buy_signals += 1
                            print(f'WMA Buy - Distance: {price_distance:.2f}%')
                        elif price_distance < -0.4:
                            sell_signals += 1
                            print(f'WMA Sell - Distance: {price_distance:.2f}%')


                    if indicator == 'ADX':
                        if df['ADX'].iloc[-1] > 20:
                            if df['ADX_pos'].iloc[-1] > df['ADX_neg'].iloc[-1]:
                                buy_signals += 1
                                print('ADX Buy')
                            else:
                                sell_signals += 1
                                print('ADX Sell')

                    if indicator == 'Vortex_pos' and 'Vortex_neg' in selected_indicators:
                        if df['Vortex_pos'].iloc[-1] > df['Vortex_neg'].iloc[-1]:
                            buy_signals += 1
                            print('VORTEX Buy')
                        elif df['Vortex_pos'].iloc[-1] < df['Vortex_neg'].iloc[-1]:
                            sell_signals += 1
                            print('VORTEX Sell')

                    if indicator == 'TRIX':
                        if df['TRIX'].iloc[-1] > 0:
                            buy_signals += 1
                            print('TRIX Buy')
                        elif df['TRIX'].iloc[-1] < 0:
                            sell_signals += 1
                            print('TRIX Sell')

                    if indicator == 'Mass_Index':
                        if df['Mass_Index'].iloc[-1] > 27:
                            sell_signals += 1
                            print('MASS INDEX Buy')
                        elif df['Mass_Index'].iloc[-1] < 26.5:
                            buy_signals += 1
                            print('MASS INDEX Sell')

                    if indicator == 'CCI':
                        if df['CCI'].iloc[-1] > 150:
                            sell_signals += 1
                            print('CCI Buy')
                        elif df['CCI'].iloc[-1] < -150:
                            buy_signals += 1
                            print('CCI Sell')

                    if indicator == 'DPO':
                        if df['DPO'].iloc[-1] > 0:
                            buy_signals += 1
                            print('DPO Buy')
                        elif df['DPO'].iloc[-1] < 0:
                            sell_signals += 1
                            print('DPO Sell')

                    if indicator == 'KST' and 'KST_signal' in selected_indicators:
                        if df['KST'].iloc[-1] > df['KST_signal'].iloc[-1]:
                            buy_signals += 1
                            print('KST Buy')
                        elif df['KST'].iloc[-1] < df['KST_signal'].iloc[-1]:
                            sell_signals += 1
                            print('KST Sell')

                    if indicator == 'Ichimoku_A' and 'Ichimoku_B' in selected_indicators:
                        if df['Ichimoku_A'].iloc[-1] > df['Ichimoku_B'].iloc[-1]:
                            buy_signals += 1
                            print('ICHIMOKU A Buy')
                        elif df['Ichimoku_A'].iloc[-1] < df['Ichimoku_B'].iloc[-1]:
                            sell_signals += 1
                            print('ICHIMOKU A Sell')

                    if indicator == 'Aroon_up' and 'Aroon_down' in selected_indicators:
                        if df['Aroon_up'].iloc[-1] > df['Aroon_down'].iloc[-1]:
                            buy_signals += 1
                            print('AROON Buy')
                        elif df['Aroon_up'].iloc[-1] < df['Aroon_down'].iloc[-1]:
                            sell_signals += 1
                            print('AROON Sell')

                    if indicator == 'STC':
                        if df['STC'].iloc[-1] > 50:
                            buy_signals += 1
                            print('STC Buy')
                        elif df['STC'].iloc[-1] < 50:
                            sell_signals += 1
                            print('STC Sell')

                    if indicator == 'RSI':
                        if df['RSI'].iloc[-1] < 25:
                            buy_signals += 1
                            print('RSI Buy')
                        elif df['RSI'].iloc[-1] > 75:
                            sell_signals += 1
                            print('RSI Sell')

                    if indicator == 'Stochastic':
                        if df['Stochastic'].iloc[-1] < 15:
                            buy_signals += 1
                            print('STOCHASTIC Buy')
                        elif df['Stochastic'].iloc[-1] > 85:
                            sell_signals += 1
                            print('STOCHASTIC Sell')

                    if indicator == 'Stochastic_signal':
                        if df['Stochastic_signal'].iloc[-1] < 15:
                            buy_signals += 1
                            print('STOCHASTIC SIGNAL Buy')
                        elif df['Stochastic_signal'].iloc[-1] > 85:
                            sell_signals += 1
                            print('STOCHASTIC SIGNAL Buy')

                    if indicator == 'TSI':
                        if df['TSI'].iloc[-1] > 0:
                            buy_signals += 1
                            print('TSI Buy')
                        elif df['TSI'].iloc[-1] < 0:
                            sell_signals += 1
                            print('TSI Sell')

                    if indicator == 'Ultimate_Oscillator':
                        if df['Ultimate_Oscillator'].iloc[-1] < 30:
                            buy_signals += 1
                            print('Ultimate_Oscillator Buy')
                        elif df['Ultimate_Oscillator'].iloc[-1] > 70:
                            sell_signals += 1
                            print('Ultimate_Oscillator Sell')

                    if indicator == 'WR':
                        if df['WR'].iloc[-1] < -80:
                            buy_signals += 1
                            print('WR Buy')
                        elif df['WR'].iloc[-1] > -20:
                            sell_signals += 1
                            print('WR Sell')

                    if indicator == 'AO':
                        if df['AO'].iloc[-1] > 0:
                            buy_signals += 1
                            print('AO Buy')
                        elif df['AO'].iloc[-1] < 0:
                            sell_signals += 1
                            print('AO Sell')

                    if indicator == 'ROC':
                        if df['ROC'].iloc[-1] > 2:
                            buy_signals += 1
                            print('ROC Buy')
                        elif df['ROC'].iloc[-1] < -2:
                            sell_signals += 1
                            print('ROC Sell')

                    if indicator == 'PVO' and 'PVO_signal' in selected_indicators:
                        if df['PVO'].iloc[-1] > df['PVO_signal'].iloc[-1]:
                            buy_signals += 1
                            print('PVO Buy')
                        elif df['PVO'].iloc[-1] < df['PVO_signal'].iloc[-1]:
                            sell_signals += 1
                            print('PVO Sell')

                    if indicator == 'PVO_hist':
                        if df['PVO_hist'].iloc[-1] > 0:
                            buy_signals += 1
                            print('PVO_hist Buy')
                        elif df['PVO_hist'].iloc[-1] < 0:
                            sell_signals += 1
                            print('PVO_hist Sell')

                    if indicator == 'Awesome_Oscillator':
                        if df['Awesome_Oscillator'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Awesome_Oscillator Buy')
                        elif df['Awesome_Oscillator'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Awesome_Oscillator Sell')

                    if indicator == 'Percentage_Price_Oscillator':
                        if df['Percentage_Price_Oscillator'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Percentage_Price_Oscillator Buy')
                        elif df['Percentage_Price_Oscillator'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Percentage_Price_Oscillator Sell')

                    if indicator == 'Percentage_Volume_Oscillator':
                        if df['Percentage_Volume_Oscillator'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Percentage_Volume_Oscillator Buy')
                        elif df['Percentage_Volume_Oscillator'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Percentage_Volume_Oscillator Sell')

                    # Volume Indicators
                    """
                    if indicator == 'OBV':
                        if df['OBV'].iloc[-1] > 0:
                            buy_signals += 1
                            print('OBV Buy')
                        elif df['OBV'].iloc[-1] < 0:
                            sell_signals += 1
                            print('OBV Sell')
                    if indicator == 'OBV':
                        if df['OBV'].iloc[-1] > df['OBV'].iloc[-2]:
                            buy_signals += 1
                            print('OBV Buy')
                        elif df['OBV'].iloc[-1] < df['OBV'].iloc[-2]:
                            sell_signals += 1
                            print('OBV Sell')
                    """
                    if indicator == 'OBV' and len(df) > 5:
                        obv_current = df['OBV'].iloc[-1]
                        obv_prev = df['OBV'].iloc[-5]
                        obv_trend = obv_current - obv_prev

                        if obv_trend > 0:
                            buy_signals += 1
                            print('OBV Buy')
                        elif obv_trend < 0:
                            sell_signals += 1
                            print('OBV Sell')



                    """
                    if indicator == 'CMF':
                        if df['CMF'].iloc[-1] > 0:
                            buy_signals += 1
                            print('CMF Buy')
                        elif df['CMF'].iloc[-1] < 0:
                            sell_signals += 1
                            print('CMF Sell')
                    """
                    if indicator == 'CMF':
                        cmf = df['CMF'].iloc[-1]

                        if cmf > 0.1:  # Flux d'argent positif fort
                            buy_signals += 1
                            print(f'CMF Buy - Value: {cmf:.3f}')
                        elif cmf < -0.1:  # Flux d'argent négatif fort
                            sell_signals += 1
                            print(f'CMF Sell - Value: {cmf:.3f}')

                    if indicator == 'FI':
                        if df['FI'].iloc[-1] > 0:
                            buy_signals += 1
                            print('FI Buy')
                        elif df['FI'].iloc[-1] < 0:
                            sell_signals += 1
                            print('FI Sell')

                    if indicator == 'EOM':
                        if df['EOM'].iloc[-1] > 0:
                            buy_signals += 1
                            print('EOM Buy')
                        elif df['EOM'].iloc[-1] < 0:
                            sell_signals += 1
                            print('EOM Sell')

                    if indicator == 'VPT':
                        if df['VPT'].iloc[-1] > df['VPT'].iloc[-2]:
                            buy_signals += 1
                            print('VPT Buy')
                        elif df['VPT'].iloc[-1] < df['VPT'].iloc[-2]:
                            sell_signals += 1
                            print('VPT Sell')

                    if indicator == 'NVI':
                        if df['NVI'].iloc[-1] > df['NVI'].iloc[-2]:
                            buy_signals += 1
                            print('NVI Buy')
                        elif df['NVI'].iloc[-1] < df['NVI'].iloc[-2]:
                            sell_signals += 1
                            print('NVI Sell')
                    """
                    if indicator == 'VWAP':
                        if df['close'].iloc[-1] > df['VWAP'].iloc[-1]:
                            buy_signals += 1
                            print('VWAP Buy')
                        elif df['close'].iloc[-1] < df['VWAP'].iloc[-1]:
                            sell_signals += 1
                            print('VWAP Sell')
                    """
                    if indicator == 'VWAP':
                        price = df['close'].iloc[-1]
                        vwap = df['VWAP'].iloc[-1]
                        price_distance = (price - vwap) / vwap * 100

                        if price_distance > 0.2:
                            buy_signals += 1
                            print(f'VWAP Buy - Distance: {price_distance:.2f}%')
                        elif price_distance < -0.2:
                            sell_signals += 1
                            print(f'VWAP Sell - Distance: {price_distance:.2f}%')

                    # Volatility Indicators
                    if indicator == 'BB_upper' and 'BB_lower' in selected_indicators:
                        if df['close'].iloc[-1] < df['BB_lower'].iloc[-1]:
                            buy_signals += 1
                            print('BB upper Buy')
                        elif df['close'].iloc[-1] > df['BB_upper'].iloc[-1]:
                            sell_signals += 1
                            print('BB upper Sell')

                    if indicator == 'BB_middle':
                        if df['close'].iloc[-1] > df['BB_middle'].iloc[-1]:
                            buy_signals += 1
                            print('BB middle Buy')
                        elif df['close'].iloc[-1] < df['BB_middle'].iloc[-1]:
                            sell_signals += 1
                            print('BB middle Sell')

                    if indicator == 'BB_percent':
                        if df['BB_percent'].iloc[-1] < 0:
                            buy_signals += 1
                            print('BB percent Buy')
                        elif df['BB_percent'].iloc[-1] > 1:
                            sell_signals += 1
                            print('BB percent Sell')

                    if indicator == 'BB_width':
                        if df['BB_width'].iloc[-1] > df['BB_width'].mean():
                            sell_signals += 1
                            print('BB width Buy')
                        elif df['BB_width'].iloc[-1] < df['BB_width'].mean():
                            buy_signals += 1
                            print('BB width Sell')

                    if indicator == 'ATR':
                        if df['ATR'].iloc[-1] > df['ATR'].mean():
                            sell_signals += 1
                            print('ATR Buy')
                        elif df['ATR'].iloc[-1] < df['ATR'].mean():
                            buy_signals += 1
                            print('ATR Sell')

                    if indicator == 'Keltner_upper' and 'Keltner_lower' in selected_indicators:
                        if df['close'].iloc[-1] < df['Keltner_lower'].iloc[-1]:
                            buy_signals += 1
                            print('Keltner upper Buy')
                        elif df['close'].iloc[-1] > df['Keltner_upper'].iloc[-1]:
                            sell_signals += 1
                            print('Keltner upper Sell')

                    if indicator == 'Keltner_middle':
                        if df['close'].iloc[-1] > df['Keltner_middle'].iloc[-1]:
                            buy_signals += 1
                            print('Keltner middle Buy')
                        elif df['close'].iloc[-1] < df['Keltner_middle'].iloc[-1]:
                            sell_signals += 1
                            print('Keltner middle Sell')

                    if indicator == 'Keltner_percent':
                        if df['Keltner_percent'].iloc[-1] < 0:
                            buy_signals += 1
                            print('Keltner percent Buy')
                        elif df['Keltner_percent'].iloc[-1] > 1:
                            sell_signals += 1
                            print('Keltner percent Sell')

                    if indicator == 'Keltner_width':
                        if df['Keltner_width'].iloc[-1] > df['Keltner_width'].mean():
                            sell_signals += 1
                            print('Keltner_width Buy')
                        elif df['Keltner_width'].iloc[-1] < df['Keltner_width'].mean():
                            buy_signals += 1
                            print('Keltner_width Sell')

                    if indicator == 'Donchian_upper' and 'Donchian_lower' in selected_indicators:
                        if df['close'].iloc[-1] < df['Donchian_lower'].iloc[-1]:
                            buy_signals += 1
                            print('Donchian_upper Buy')
                        elif df['close'].iloc[-1] > df['Donchian_upper'].iloc[-1]:
                            sell_signals += 1
                            print('Donchian_upper Sell')

                    if indicator == 'Donchian_middle':
                        if df['close'].iloc[-1] > df['Donchian_middle'].iloc[-1]:
                            buy_signals += 1
                            print('Donchian_middle Buy')
                        elif df['close'].iloc[-1] < df['Donchian_middle'].iloc[-1]:
                            sell_signals += 1
                            print('Donchian_middle Sell')

                    if indicator == 'Donchian_percent':
                        if df['Donchian_percent'].iloc[-1] < 0:
                            buy_signals += 1
                            print('Donchian_percent Buy')
                        elif df['Donchian_percent'].iloc[-1] > 1:
                            sell_signals += 1
                            print('Donchian_percent Sell')

                    if indicator == 'Donchian_width':
                        if df['Donchian_width'].iloc[-1] > df['Donchian_width'].mean():
                            sell_signals += 1
                            print('Donchian_width Buy')
                        elif df['Donchian_width'].iloc[-1] < df['Donchian_width'].mean():
                            buy_signals += 1
                            print('Donchian_width Sell')

                    if indicator == 'Ulcer_Index':
                        if df['Ulcer_Index'].iloc[-1] > df['Ulcer_Index'].mean():
                            sell_signals += 1
                            print('Ulcer_Index Buy')
                        elif df['Ulcer_Index'].iloc[-1] < df['Ulcer_Index'].mean():
                            buy_signals += 1
                            print('Ulcer_Index Sell')

                    if indicator == 'True_Range':
                        if df['True_Range'].iloc[-1] > df['True_Range'].mean():
                            sell_signals += 1
                            print('True Range Buy')
                        elif df['True_Range'].iloc[-1] < df['True_Range'].mean():
                            buy_signals += 1
                            print('True Range Sell')

                    if indicator == 'Daily_Return':
                        if df['Daily_Return'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Daily_Return Buy')
                        elif df['Daily_Return'].iloc[-1] < 0:
                            sell_signals += 1
                            print('Daily_Return Sell')

                    if indicator == 'Cumulative_Return':
                        if df['Cumulative_Return'].iloc[-1] > df['Cumulative_Return'].mean():
                            buy_signals += 1
                            print('Cumulative_Return Buy')
                        elif df['Cumulative_Return'].iloc[-1] < df['Cumulative_Return'].mean():
                            sell_signals += 1
                            print('Cumulative_Return Sell')

                    if indicator == 'Daily_Log_Return':
                        if df['Daily_Log_Return'].iloc[-1] > 0:
                            buy_signals += 1
                            print('Daily_Log_Return Buy')
                        elif df['Daily_Log_Return'].iloc[-1] < 0:
                            sell_signals += 1

                    indicators_examined += 1
                except Exception as e:
                    self.display_to_status_bar("Error", f"Error processing indicator '{indicator}': {e}")
                    continue

            score = buy_signals - sell_signals

            if buy_signals > threshold * indicators_examined:
                colored_score = color_for_message('buy', score)
                formatted_message = f"Buy Signal {symbol} Triggered in {timeframe}"
                self.display_to_nano_terminal(formatted_message, colored_score, "info")
                return 'buy', score
            elif sell_signals > threshold * indicators_examined:
                colored_score = color_for_message('sell', score)
                formatted_message = f"Sell Signal {symbol} Triggered in {timeframe}"
                self.display_to_nano_terminal(formatted_message, colored_score, "info")
                return 'sell', score
            else:
                colored_score = color_for_message('No Action', score)
                formatted_message = f"No Action Signal {symbol} Triggered in {timeframe}"
                self.display_to_nano_terminal(formatted_message, colored_score, "info")
                return "no_action", score


        except Exception as e:
            self.display_to_status_bar("Error", f"Error processing indicators: {e}")
            return None, 0



    def calculate_weighted_score(self, combined_scores):
        if not combined_scores:
            return None

        weight_dict = {
            '1s': 0.005,
            '1m': 0.01,
            '3m': 0.02,
            '5m': 0.03,
            '15m': 0.04,
            '30m': 0.05,
            '1h': 0.1,
            '2h': 0.15,
            '4h': 0.2,
            '6h': 0.3,
            '8h': 0.4,
            '12h': 0.5,
            '1d': 0.6,
            '3d': 0.7,
            '1w': 0.8,
            '1M': 1.0,
            '3M': 1.0
        }

        weighted_score = sum(score * weight_dict[tf] for tf, score in combined_scores)
        return weighted_score

    def update_button_states(self):
        if self.start_button and self.stop_button:
            if self.nano_trading_active:
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
            else:
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)


    def check_and_start_trading(self):
        if self.nano_trading_active:
            self.display_to_status_bar("Error", "Nano trading bot is already running.")
            return

        selected_exchanges = [exchange for exchange, var in self.nano_trading_exchange_vars.items() if var.isChecked()]
        if not selected_exchanges:
            self.display_to_status_bar("Error", "No exchanges selected for trading.")
            return

        self.nano_trading_selected_exchanges = selected_exchanges

        # Mettre à jour immédiatement la status bar avec le nombre d'échanges sélectionnés
        self.update_enhanced_status_bar("🔄 Preparing", "Exchange Selection", {}, len(selected_exchanges), 0, 0.0)

        # Vérifier que les conditions d'achat et de vente sont remplies avant de démarrer
        if not self.check_conditions_filled():
            #self.show_error_message("You must fill at least one buy condition and one sell condition before starting the bot.")
            return


        # Afficher le texte au démarrage du bot dans (2,2)
        if hasattr(self.dashboard, 'exchanges_trades_tables'):
            self.dashboard.exchanges_trades_tables.show_starting_message()

        # Mettre à jour les balances des échanges
        self.update_exchanges_balances()

        # Mettre à jour le widget ConnectedExchangesBalancesWidget avec les échanges sélectionnés
        self.connected_exchanges_widget.initialize_active_exchanges()

        # Démarrer le bot
        self.start_nano_trading()






    def check_and_restart_trading(self):
         # Mettre à jour les balances des échanges
        self.update_exchanges_balances()

        # Démarrer le bot
        self.start_nano_trading()



    def start_nano_trading(self):
        # Clean up any existing threads and workers
        if self.trading_thread:
            self.trading_thread.quit()
            self.trading_thread.wait()
            self.trading_thread = None

        if self.trading_worker:
            self.trading_worker = None

        # Create new worker and thread
        self.trading_worker = TradingWorker(self, self.nano_processor, self.dashboard, self.db)
        self.trading_thread = QThread()

        # Move worker to thread
        self.trading_worker.moveToThread(self.trading_thread)

        # Connect signals
        self.trading_worker.update_terminal.connect(self.display_to_nano_terminal)
        self.trading_worker.update_status.connect(self.display_to_status_bar)
        self.trading_worker.update_enhanced_status.connect(self.update_enhanced_status_bar)
        self.trading_worker.add_trade_signal.connect(self.dashboard.exchanges_trades_tables.add_trade_to_table)
        self.trading_worker.update_exchanges_balances_signal.connect(self.update_exchanges_balances)
        self.trading_worker.update_statistics_signal.connect(
            self.dashboard.exchange_symbol_trade_data_widget.update_widget_content
        )
        self.trading_worker.trading_finished.connect(self.on_trading_finished)

        # Start the thread
        self.trading_thread.start()

        # Invoke start_trading in the worker's thread
        QMetaObject.invokeMethod(self.trading_worker, 'start_trading', Qt.QueuedConnection)

        # Initialiser la barre de statut avec le bon nombre d'échanges sélectionnés
        selected_exchanges_count = len(self.nano_trading_selected_exchanges)
        self.update_enhanced_status_bar("🚀 Starting", "Bot Initialization", {}, selected_exchanges_count, 0, 0.0)

        # Start the PriceWatcher
        self.nano_processor.start_watching()

        # Connect signals from the PriceWatcherWorker
        self.nano_processor.worker.trade_updated_signal.connect(
            self.dashboard.exchanges_trades_tables.update_trade_in_ui
        )
        self.nano_processor.worker.trade_profit_signal.connect(
            self.dashboard.connected_exchanges_widget.update_profits
        )
        self.nano_processor.worker.update_enhanced_status.connect(self.update_enhanced_status_bar)
        # self.nano_processor.worker.trade_removed_from_db_signal.connect(
        #     self.dashboard.exchanges_trades_tables.remove_trade_from_table
        # )
        self.db.trade_removed_from_db_signal.connect(
            self.dashboard.exchanges_trades_tables.remove_trade_from_table
        )
        self.nano_processor.worker.graph_removed_signal.connect(
            self.dashboard.exchange_symbol_trade_data_widget.remove_symbol_from_graph
        )

        # Update bot state and buttons
        self.nano_trading_active = True
        self.update_button_states()

        # Start the bot timer
        if hasattr(self, 'main_window') and self.main_window:
            self.main_window.start_bot_timer()

        self.display_to_nano_terminal("Info", "Nano trading bot has been started.", "info")







    def fetch_and_update_balances(self, exchange_name):
        exchange_class = getattr(ccxt, exchange_name)
        exchange = exchange_class({
            'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
            'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
            'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
        })

        try:
            balance = exchange.fetch_balance()
            # Update the balance_initial_by_currency dictionary
            self.balance_initial_by_currency['USDT'] += balance['free'].get('USDT', 0)
            self.balance_initial_by_currency['USDC'] += balance['free'].get('USDC', 0)
            self.balance_initial_by_currency['BTC'] += balance['free'].get('BTC', 0)

            # If using a custom symbol
            custom_symbol = self.get_selected_currency()
            if custom_symbol and custom_symbol not in ['USDT', 'USDC', 'BTC']:
                self.balance_initial_by_currency[custom_symbol] += balance['free'].get(custom_symbol, 0)

            self.display_to_nano_terminal("Info", f"Balance for {exchange_name}: {balance['free']}", "info")
            # Passez les échanges sélectionnés directement à la méthode




        except Exception as e:
            self.display_to_status_bar("Error", f"Error fetching balance for {exchange_name}: {e}")


    def update_exchanges_balances(self):
        balances = []
        selected_currency = self.get_selected_currency()
        # Initialize balance_initial_by_currency
        self.balance_initial_by_currency = {}
        tokens_to_check = ['USDT', 'USDC', 'BTC']
        if selected_currency and selected_currency not in tokens_to_check:
            tokens_to_check.append(selected_currency)
        for token in tokens_to_check:
            self.balance_initial_by_currency[token] = 0
        for exchange_name in self.nano_trading_selected_exchanges:
            exchange_class = getattr(ccxt, exchange_name)
            exchange = exchange_class({
                'apiKey': os.getenv(f'{exchange_name.upper()}_API_KEY'),
                'secret': os.getenv(f'{exchange_name.upper()}_SECRET_KEY'),
                'password': os.getenv(f'{exchange_name.upper()}_PASSWORD', None)
            })

            try:
                balance = exchange.fetch_balance()
                for token in tokens_to_check:
                    if token in balance['free']:
                        balances.append({
                            'exchange': exchange_name,
                            'token': token,
                            'balance': balance['free'][token]
                        })
                        # Update initial balance
                        self.balance_initial_by_currency[token] += balance['free'][token]
            except Exception as e:
                self.display_to_nano_terminal("Error", f"Error fetching balance for {exchange_name}: {e}", "error")

        # Update the balances in the connected widget
        self.connected_exchanges_widget.update_balances(balances)



    def get_active_trades_count(self):
        """
        Retourne le nombre de trades actifs (ceux qui ne sont pas encore terminés).
        """
        # Récupérer les trades depuis la base de données
        active_trades = self.db.get_all_trades()

        return len(active_trades)



    def calculate_average_buy_price(self, symbol, existing_trade_id, last_close, amount_to_trade):
        try:
            # Récupérer les détails du trade existant (s'il existe)
            existing_trade = self.db.get_trade_by_id(existing_trade_id)

            # Si un trade existe déjà, on met à jour le prix moyen d'achat
            if existing_trade:
                # Quantité totale déjà achetée
                existing_amount_traded = existing_trade['amount_to_trade']

                # Prix moyen d'achat actuel
                existing_average_price = existing_trade['buy_price']

                # Calculer le nouveau montant total
                total_amount_traded = existing_amount_traded + amount_to_trade

                # Calculer la valeur totale actuelle du trade
                total_value_traded = (existing_amount_traded * existing_average_price) + (amount_to_trade * last_close)

                # Calculer et retourner le nouveau prix moyen d'achat
                new_average_price = total_value_traded / total_amount_traded
                return new_average_price
            else:
                # Si aucun trade existant, on retourne simplement le prix actuel
                return last_close
        except Exception as e:
            print(f"Error calculating average buy price for {symbol}: {e}")
            return last_close

    def calculate_last_buy_price(self, symbol, existing_trade_id, last_close):
        """
        Retourne le dernier prix d'achat enregistré pour ce trade.
        Si c'est le premier achat, retourne le prix actuel (last_close).
        """
        try:
            if existing_trade_id:
                # Récupérer le trade existant
                trade = self.db.get_trade_by_id(existing_trade_id)
                if trade and trade.get('buy_price') is not None:
                    return trade['buy_price']
            return last_close
        except Exception as e:
            print(f"Erreur lors de la récupération du dernier prix d'achat pour {symbol}: {e}")
            return last_close
            
    def calculate_stop_loss_price(self, average_buy_price, stop_loss_percent):
        """
        Calcule le prix de vente pour un stop loss donné.
        
        Args:
            average_buy_price: Prix d'achat moyen du trade
            stop_loss_percent: Pourcentage de perte à appliquer (valeur positive, ex: 5 pour 5%)
            
        Returns:
            float: Prix de vente pour le stop loss
        """
        # Convertir le pourcentage en décimal (ex: 5% -> 0.05)
        stop_loss_decimal = stop_loss_percent / 100.0
        
        # Calculer le prix de vente (soustraire la perte)
        stop_loss_price = average_buy_price * (1 - stop_loss_decimal)
        
        return stop_loss_price
        
    def calculate_fixed_profit_sell_price(self, buy_price, trade_id, exchange=None):
        """
        Calcule le prix de vente pour atteindre un profit fixe
        dans la devise sélectionnée par l'utilisateur.
        
        Args:
            buy_price: Prix d'achat unitaire dans la devise de base
            trade_id: ID du trade pour récupérer la quantité totale à vendre
            exchange: Instance de l'échange pour récupérer les frais (optionnel)
            
        Returns:
            float: Prix de vente nécessaire pour atteindre le profit fixe ou None en cas d'erreur
        """
        # Vérification des paramètres d'entrée
        if not trade_id:
            error_msg = "[FIXED_PROFIT][ERREUR] L'ID du trade est requis pour le calcul du profit fixe"
            print(error_msg)
            self.update_terminal.emit("Error", error_msg, "error")
            return None
            
        if not buy_price or buy_price <= 0:
            error_msg = f"[FIXED_PROFIT][ERREUR] Prix d'achat invalide: {buy_price}"
            print(error_msg)
            self.update_terminal.emit("Error", error_msg, "error")
            return None
        
        # Initialisation des logs de débogage
        debug_prefix = f"[FIXED_PROFIT][Trade {trade_id}]"
        print(f"{debug_prefix} Début du calcul du prix de vente avec profit fixe")
        print(f"{debug_prefix} - Prix d'achat: {buy_price}")
        
        try:
            # Récupérer les détails du trade depuis la base de données
            print(f"{debug_prefix} Récupération des détails du trade...")
            trade = self.db.get_trade_by_id(trade_id)
            
            if not trade:
                error_msg = f"{debug_prefix} Aucun trade trouvé avec cet ID"
                print(error_msg)
                self.update_terminal.emit("Error", error_msg, "error")
                return None
                
            symbol = trade.get('symbol', 'inconnu')
            print(f"{debug_prefix} Trade trouvé - Symbole: {symbol}")
            
            # Récupérer la quantité à vendre
            amount_to_sell = trade.get('amount_to_trade')
            if not amount_to_sell or amount_to_sell <= 0:
                error_msg = f"{debug_prefix} Quantité à vendre invalide: {amount_to_sell}"
                print(error_msg)
                self.update_terminal.emit("Error", error_msg, "error")
                return None
                
            print(f"{debug_prefix} - Quantité à vendre: {amount_to_sell}")
            
            # Récupérer les frais d'échange
            exchange_fees = self._get_exchange_fees(exchange)
            
            # Récupérer le montant du profit fixe
            fixed_profit_amount = self._get_valid_fixed_profit_amount()
            if fixed_profit_amount is None:
                return None
                
            selected_currency = self.get_selected_currency()
            
            # Calcul du coût total d'achat
            total_cost = buy_price * amount_to_sell
            
            # Calcul du prix de vente nécessaire pour atteindre le profit fixe
            # Formule: (Coût total + Profit fixe) / (Quantité * (1 - frais))
            try:
                selling_price = (total_cost + fixed_profit_amount) / (amount_to_sell * (1 - exchange_fees))
            except ZeroDivisionError:
                error_msg = f"{debug_prefix} Erreur de division par zéro lors du calcul du prix de vente"
                print(error_msg)
                self.update_terminal.emit("Error", error_msg, "error")
                return None
            
            # Vérifier que le prix de vente est valide
            if selling_price <= buy_price:
                warning_msg = (
                    f"{debug_prefix} Le prix de vente calculé ({selling_price}) est inférieur ou égal "
                    f"au prix d'achat ({buy_price}). Ajustement automatique..."
                )
                print(warning_msg)
                self.update_terminal.emit("Warning", warning_msg, "warning")
                
                # On ajoute une marge minimale de 0.1% pour éviter les boucles de vente immédiate
                selling_price = buy_price * 1.001
            
            # Logs de débogage détaillés
            print(f"{debug_prefix} Calcul du prix de vente avec profit fixe:")
            print(f"{debug_prefix} - Symbole: {symbol}")
            print(f"{debug_prefix} - Prix d'achat unitaire: {buy_price}")
            print(f"{debug_prefix} - Quantité à vendre: {amount_to_sell}")
            print(f"{debug_prefix} - Coût total d'achat: {total_cost}")
            print(f"{debug_prefix} - Profit fixe cible: {fixed_profit_amount} {selected_currency}")
            print(f"{debug_prefix} - Frais d'échange: {exchange_fees*100:.4f}%")
            print(f"{debug_prefix} - Prix de vente calculé: {selling_price}")
            print(f"{debug_prefix} - Marge bénéficiaire: {((selling_price - buy_price) / buy_price * 100):.4f}%")
            
            # Arrondir le prix de vente à 8 décimales pour éviter les problèmes de précision
            selling_price = round(selling_price, 8)
            
            # Vérification finale de la validité du prix
            if selling_price <= 0:
                error_msg = f"{debug_prefix} Le prix de vente calculé est invalide: {selling_price}"
                print(error_msg)
                self.update_terminal.emit("Error", error_msg, "error")
                return None
            
            return selling_price
            
        except Exception as e:
            print(f"Erreur dans le calcul du prix de vente avec profit fixe: {e}")
            return None
            
    def _get_exchange_fees(self, exchange):
        """
        Récupère les frais de trading depuis l'API de l'exchange.
        
        Args:
            exchange: Instance de l'échange (peut être None)
            
        Returns:
            float: Taux de frais de trading (par défaut 0.1% si non disponible)
        """
        exchange_fees = 0.001  # Valeur par défaut 0.1%
        
        if exchange is not None:
            try:
                # Essayer de récupérer les frais depuis l'API de l'exchange
                market_info = exchange.load_markets()
                if market_info and hasattr(exchange, 'fees'):
                    trading_fees = exchange.fees.get('trading', {})
                    if 'taker' in trading_fees:
                        exchange_fees = float(trading_fees['taker'])
                        print(f"[FIXED_PROFIT] Frais de trading récupérés depuis l'exchange: {exchange_fees*100}%")
            except Exception as e:
                print(f"[FIXED_PROFIT] Impossible de récupérer les frais de l'exchange, utilisation de la valeur par défaut (0.1%): {e}")
        
        return exchange_fees
        
    def _get_valid_fixed_profit_amount(self):
        """
        Récupère et valide le montant du profit fixe saisi par l'utilisateur.
        
        Returns:
            float: Le montant du profit fixe validé, ou None en cas d'erreur
        """
        try:
            fixed_profit_amount = float(self.get_fixed_profit_amount())
            if fixed_profit_amount <= 0:
                error_msg = f"[FIXED_PROFIT][ERREUR] Le montant du profit fixe doit être supérieur à 0: {fixed_profit_amount}"
                print(error_msg)
                self.update_terminal.emit("Error", error_msg, "error")
                return None
            return fixed_profit_amount
        except (ValueError, TypeError) as e:
            error_msg = f"[FIXED_PROFIT][ERREUR] Format du profit fixe invalide: {e}"
            print(error_msg)
            self.update_terminal.emit("Error", error_msg, "error")
            return None
        
    def calculate_fixed_profit_percentage(self, buy_price, trade_id, exchange=None):
        """
        Calcule le pourcentage de profit nécessaire pour atteindre un profit fixe
        dans la devise sélectionnée par l'utilisateur.
        
        Args:
            buy_price: Prix d'achat unitaire dans la devise de base
            trade_id: ID du trade pour récupérer la quantité totale à vendre
            exchange: Instance de l'échange pour récupérer les frais (optionnel)
            
        Returns:
            float: Pourcentage de profit nécessaire pour atteindre le profit fixe
        """
        try:
            # Récupérer les détails du trade depuis la base de données
            trade = self.db.get_trade_by_id(trade_id)
            if not trade:
                print(f"Aucun trade trouvé avec l'ID: {trade_id}")
                return None
                
            # Récupérer la quantité totale à vendre
            amount_to_sell = trade.get('amount_to_trade')
            if not amount_to_sell or amount_to_sell <= 0:
                print(f"Quantité à vendre invalide pour le trade {trade_id}: {amount_to_sell}")
                return None
            
            # Récupérer les frais d'échange depuis l'API de l'exchange
            exchange_fees = 0.001  # Valeur par défaut 0.1%
            
            if exchange is not None:
                try:
                    # Essayer de récupérer les frais depuis l'API de l'exchange
                    market_info = exchange.load_markets()
                    if market_info and hasattr(exchange, 'fees'):
                        trading_fees = exchange.fees.get('trading', {})
                        if 'taker' in trading_fees:
                            exchange_fees = float(trading_fees['taker'])
                except Exception as e:
                    print(f"Impossible de récupérer les frais de l'exchange, utilisation de la valeur par défaut (0.1%): {e}")
            
            # Récupérer le montant du profit fixe dans la devise sélectionnée
            fixed_profit_amount = float(self.get_fixed_profit_amount())
            
            # Calcul du prix de vente nécessaire
            cost = buy_price * amount_to_sell
            selling_price = (cost + fixed_profit_amount) / (amount_to_sell * (1 - exchange_fees))
            
            # Calcul du pourcentage de profit
            profit_percent = ((selling_price - buy_price) / buy_price) * 100
            
            return profit_percent
            
        except Exception as e:
            print(f"Erreur dans le calcul du profit fixe: {e}")
            return None
   

    def calculate_price_to_buy(self, row, average_buy_price):
        try:
            # S'assurer que percentage_combobox est un QComboBox et obtenir son texte
            if isinstance(row["percentage_combobox"], QComboBox):
                buy_percentage = float(row["percentage_combobox"].currentText().replace("%", "")) / 100
            else:
                # Si c'est déjà un str, utilisez directement la valeur
                buy_percentage = float(row["percentage_combobox"].replace("%", "")) / 100

            # Calculer le prix d'achat si différent du précédent
            buy_price = average_buy_price * (1 - buy_percentage)
            if buy_price != self.previous_buy_price:
                self.previous_buy_price = buy_price
                print(f"Calculated buy price: {buy_price} for percentage: {buy_percentage}")
            return buy_price

        except Exception as e:
            print(f"Error calculating price to buy: {e}")
            return average_buy_price

    def calculate_price_to_sell(self, row, average_buy_price, trade_id=None, actual_buy_price=None):
        """
        Calcule le prix de vente en fonction du mode de calcul de profit sélectionné.
        
        Args:
            row: Dictionnaire contenant les informations de la ligne de vente
            average_buy_price: Prix d'achat moyen ou dernier prix d'achat selon le mode
            trade_id: ID du trade pour récupérer la quantité totale à vendre (obligatoire en mode fixed_profit)
            actual_buy_price: Prix d'achat réel après exécution de l'ordre (optionnel)
            
        Returns:
            float: Prix de vente calculé ou None si le prix réel n'est pas encore disponible
        """
        profit_mode = self.get_profit_calculation_mode()
        
        # Si on est en mode fixed_profit et qu'on n'a pas encore le prix d'achat réel
        if profit_mode == 'fixed_profit' and actual_buy_price is None:
            print("[INFO] Le prix de vente en mode Fixed Profit sera déterminé après l'exécution de l'ordre d'achat")
            print("[INFO] Le calcul utilisera le prix d'achat réel pour déterminer le prix de vente avec le profit fixe")
            return None
        
        # Mode average_buy_price ou last_buy_price
        if profit_mode in ['average_buy_price', 'last_buy_price']:
            try:
                # S'assurer que percentage_combobox est un QComboBox et obtenir son texte
                if isinstance(row["percentage_combobox"], QComboBox):
                    sell_percentage = float(row["percentage_combobox"].currentText().replace("%", "")) / 100
                else:
                    # Si c'est déjà un str, utilisez directement la valeur
                    sell_percentage = float(row["percentage_combobox"].replace("%", "")) / 100

                # Utiliser le prix d'achat réel s'il est disponible, sinon utiliser le prix moyen
                buy_price = actual_buy_price if actual_buy_price is not None else average_buy_price
                
                # Calculer le prix de vente si différent du précédent
                sell_price = buy_price * (1 + sell_percentage)
                if sell_price != self.previous_sell_price:
                    self.previous_sell_price = sell_price
                    print(f"Calculated sell price: {sell_price} for percentage: {sell_percentage*100}%")
                return sell_price

            except Exception as e:
                print(f"Error calculating price to sell: {e}")
                return average_buy_price
                
        # Mode fixed_profit avec prix d'achat réel
        elif profit_mode == 'fixed_profit':
            if not trade_id:
                print("Erreur: L'ID du trade est requis pour le calcul du profit fixe")
                return average_buy_price
                
            try:
                # Utiliser le prix d'achat réel pour le calcul du prix de vente
                sell_price = self.calculate_fixed_profit_sell_price(actual_buy_price, trade_id)
                
                if sell_price is None:
                    raise ValueError("Impossible de calculer le prix de vente avec profit fixe")
                
                if sell_price != self.previous_sell_price:
                    self.previous_sell_price = sell_price
                    print(f"Calculated fixed profit sell price: {sell_price} for fixed profit amount: {self.get_fixed_profit_amount()} {self.get_selected_currency()}")
                
                return sell_price
                
            except Exception as e:
                print(f"Erreur dans le calcul du prix de vente avec profit fixe: {e}")
                return average_buy_price
                
    


##############################################################
# Shutdown Program

    def shutdown_application(self):
        reply = QMessageBox.question(
            None,
            'Confirmation',
            'Are you sure you want to shutdown the application?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Arrêter le trading
            self.delete_database()
            self.stop_nano_trading()

            # Arrêter le PriceWatcher
            if self.nano_processor:
                try:
                    if self.nano_processor.worker:

                        # Déconnecter les signaux du worker
                        try:
                            self.nano_processor.worker.update_terminal.disconnect(self.display_to_nano_terminal)
                            # Déconnecter les autres signaux si nécessaire
                        except RuntimeError:
                            pass

                    self.nano_processor.stop_watching()
                    self.nano_processor = None
                except Exception as e:
                    print(f"Error stopping nano_processor: {e}")

            # S'assurer que tous les événements sont traités
            QCoreApplication.processEvents()

            # Fermer l'application
            QApplication.quit()
        else:
            pass


##############################################################
