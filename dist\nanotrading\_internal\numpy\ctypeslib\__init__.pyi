import ctypes
from ctypes import c_int64 as _c_intp

from ._ctypeslib import (
    __all__ as __all__,
)
from ._ctypeslib import (
    __doc__ as __doc__,
)
from ._ctypeslib import (
    _concrete_ndptr as _concrete_ndptr,
)
from ._ctypeslib import (
    _ndptr as _ndptr,
)
from ._ctypeslib import (
    as_array as as_array,
)
from ._ctypeslib import (
    as_ctypes as as_ctypes,
)
from ._ctypeslib import (
    as_ctypes_type as as_ctypes_type,
)
from ._ctypeslib import (
    c_intp as c_intp,
)
from ._ctypeslib import (
    load_library as load_library,
)
from ._ctypeslib import (
    ndpointer as ndpointer,
)
